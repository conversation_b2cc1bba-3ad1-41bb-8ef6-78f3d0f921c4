#!/usr/bin/env python3

import os
import sys
import base64
import time
import netmiko
import getpass
import pandas as pd # type: ignore
from base64 import b64decode as b64d
from multiprocessing import Manager, Pool, cpu_count
from netmiko.f5.f5_tmsh_ssh import F5TmshSSH as f5t
from netmiko.f5.f5_linux_ssh import F5LinuxSSH as f5l
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email.mime.text import MIMEText
from email.utils import formatdate, COMMASPACE
from email import encoders

# Consolidate exceptions
netmiko_exceptions = (netmiko.exceptions.NetMikoTimeoutException,
                      netmiko.exceptions.NetMikoAuthenticationException,
                      IOError)

# Setup paths and files
basedir = os.path.dirname(os.path.abspath(__file__))
user_admin = getpass.getuser()
outdir = os.path.join(basedir, 'output')
inpdir = os.path.join(basedir, 'input')
input_csv = os.path.join(inpdir, 'cyberark_out_secrets.csv')
day = time.strftime('%b_%d_%y')
logfile = os.path.join(outdir, f'ad_hoc_f5_{day}.txt')
temp_logfile = os.path.join(outdir, f'temp_ad_hoc_f5_{day}.txt')

# Create output directory if it doesn't exist
if not os.path.exists(outdir):
    os.makedirs(outdir)

# Remove temp log file if it exists
if os.path.exists(temp_logfile):
    os.remove(temp_logfile)

# Import secrets
sec_path = b'L2hvbWUvcHNvbWFzMTc4L3NpZV9wcm9qZWN0cy9zaWVfd2NvL2lucHV0'
secr_path = b64d(sec_path).decode('utf-8')
sys.path.insert(1, secr_path)
from sie_get_secrets import * # type: ignore
u = get_sec(f5_usr) # type: ignore

# Load CSV data
try:
    input_df = pd.read_csv(input_csv)
except FileNotFoundError:
    print(f"Error: Input file {input_csv} not found")
    sys.exit(1)

def sendMail(sfrom, to, subject, text, files=None):
    """Send email with optional attachments"""
    if files is None:
        files = []
        
    msg = MIMEMultipart()
    msg['To'] = COMMASPACE.join(to)
    msg['Date'] = formatdate(localtime=True)
    msg['Subject'] = subject
    msg.attach(MIMEText(text))
    
    for file in files:
        try:
            with open(file, "rb") as f:
                part = MIMEBase('application', "octet-stream")
                part.set_payload(f.read())
                encoders.encode_base64(part)
                part.add_header('Content-Disposition', 
                               f'attachment; filename="{os.path.basename(file)}"')
                msg.attach(part)
        except FileNotFoundError:
            print(f"Warning: Attachment {file} not found")
    
    try:
        server = smtplib.SMTP('mailrelay.comcast.com')
        server.starttls()
        server.sendmail(sfrom, to, msg.as_string())
        server.quit()
    except smtplib.SMTPException as e:
        print(f"Error sending email: {e}")

def connect_device(ip, p, adm_opt):
    """Connect to F5 device based on admin option"""
    if adm_opt in ['1', '3', '4', '5']:
        return f5l(ip=ip, username=u, password=p)
    else:
        return f5t(ip=ip, username=u, password=p)


def get_command_option():
    if user_admin not in ['psomas178', 'sayub200', 'sfishe958', 'ssemaa361', 'kpatel216', 'rrawda200']:
        print('\nSorry you dont have access to run this script :-)\n')
        exit()
    print('Do you want to fetch latest passwords from CyberArk - Pls Note that the passwords will be valid for 90 days and you dont have to perform this step during every run\n\n')
    print('#'*64 + '\n')
    print('#' + '{:<62}'.format('1 - Fetch CyberArk Password') + '#')
    print('#' + '{:<62}'.format('2 - Dont fetch CyberArk Password') + '#')
    cybark_option = input('What will be your choice? : ')
    if cybark_option == '1':
        os.system('/home/<USER>/anaconda3/envs/SIE_WCO/bin/python /home/<USER>/sie_projects/sie_wco/input/cybark_secret_retrieve.py')
    elif cybark_option == '2':
        pass
    else:
        print('\n\nIncorrect Option Selected, Exiting out...\n\n')
        exit()
    while True:
        print('Select from options below...\n\n')
        print('#'*64 + '\n')
        print('#' + '{:<62}'.format('1 - Domain Bypass from Web-Filtering') + '#')
        print('#' + '{:<62}'.format('2 - Domain Bypass from only SSL Decryption') + '#')
        print('#' + '{:<62}'.format('3 - IPv4/IPv6 Bypass from Web-Filtering') + '#')
        print('#' + '{:<62}'.format('4 - Problem IPv6 Drop on WCO') + '#')
        print('#' + '{:<62}'.format('5 - AdHoc Commands') + '#')
        print('Select from options below...\n\n')
        admin_option = input('What will be your choice? : ')
        if admin_option == '5':
            if user_admin in ['psomas178']:
                return admin_option
            else:
                print('\nYou dont have access to run this command\n')
                continue
        if admin_option not in ['1', '2', '3', '4']:
            print('\n\nStop playing around select one of the available options...\n\n')
            continue
        else:
            return admin_option

def get_commands_dombyp():
    commands = []
    print('#'*64 + '\n')
    print('Enter the domains to be bypassed from WF...\n')
    while True:
        command = input("Domain: ")
        if command == '':
            break
        else:
            commands.append('tmsh modify ltm data-group internal domain_ends_with records add { ' + command + ' }')
    commands.append('tmsh save sys config')
    print('\n' + '#'*64)
    print('#' + '{:^62}'.format('Commands to be executed...') + '#')
    print('#'*64 + '\n')
    for d in commands:
        print(d)
    confirmation = input("Please Confirm[y/Y/yes/Yes]: ")
    if confirmation.lower() in ['y', 'yes']:
        pass
    else:
        print('\n\n\n\nCONFIRMATION NOT PROVIDED!!! THEREFORE EXITING THE SCRIPT!!!\n\n\n\n')
        exit()
    return commands

def get_commands_domsslbyp():
    commands = []
    print('#'*64 + '\n')
    print('Enter the domains to be SSL Decryption bypassed...\n')
    while True:
        command = input("Domain: ")
        if command == '':
            break
        else:
            commands.append('modify sys url-db url-category ccs_custombypass urls add { https://\*' + command + '/ {type glob-match} }')
    commands.append('save sys config')
    print('\n' + '#'*64)
    print('#' + '{:^62}'.format('Commands to be executed...') + '#')
    print('#'*64 + '\n')
    for d in commands:
        print(d)
    confirmation = input("Please Confirm[y/Y/yes/Yes]: ")
    if confirmation.lower() in ['y', 'yes']:
        pass
    else:
        print('\n\n\n\nCONFIRMATION NOT PROVIDED!!! THEREFORE EXITING THE SCRIPT!!!\n\n\n\n')
        exit()
    return commands

def get_commands_ipbyp():
    commands = []
    print('#'*64 + '\n')
    print('Enter the IPv4/IPv6 to be bypassed from WF...\n')
    while True:
        command = input("IP: ")
        if command == '':
            break
        else:
            ip_data = input("Data: ")
            if ip_data == '':
                print("Must provide data value for the IP being bypassed - e.g. MS, Office365, AWS, etc.")
                continue
            commands.append('tmsh modify ltm data-group internal bypass_dg1 records add { ' + command + ' {data ' + ip_data + '} }')
    commands.append('tmsh save sys config')
    print('\n' + '#'*64)
    print('#' + '{:^62}'.format('Commands to be executed...') + '#')
    print('#'*64 + '\n')
    for d in commands:
        print(d)
    confirmation = input("Please Confirm[y/Y/yes/Yes]: ")
    if confirmation.lower() in ['y', 'yes']:
        pass
    else:
        print('\n\n\n\nCONFIRMATION NOT PROVIDED!!! THEREFORE EXITING THE SCRIPT!!!\n\n\n\n')
        exit()
    return commands

def get_commands_probip6drp():
    commands = []
    print('#'*64 + '\n')
    print('Enter the Problem IPv6 to be dropped on WCO...\n')
    while True:
        command = input("IPv6: ")
        if command == '':
            break
        else:
            ip_data = input("Data: ")
            if ip_data == '':
                print("Must provide data value for the IPv6 being dropped - e.g. MS, Office365, AWS, etc.")
                continue
            commands.append('tmsh modify ltm data-group internal problem_ipv6_dg records add { ' + command + ' {data ' + ip_data + '} }')
    commands.append('tmsh save sys config')
    print('\n' + '#'*64)
    print('#' + '{:^62}'.format('Commands to be executed...') + '#')
    print('#'*64 + '\n')
    for d in commands:
        print(d)
    confirmation = input("Please Confirm[y/Y/yes/Yes]: ")
    if confirmation.lower() in ['y', 'yes']:
        pass
    else:
        print('\n\n\n\nCONFIRMATION NOT PROVIDED!!! THEREFORE EXITING THE SCRIPT!!!\n\n\n\n')
        exit()
    return commands

def get_commands():
    commands = []
    print('#'*64 + '\n')
    print('Enter the commands to be executed...\n')
    while True:
        command = input("Command: ")
        if command == '':
            break
        else:
            commands.append(command)
    print('\n' + '#'*64)
    print('#' + '{:^62}'.format('Commands to be executed...') + '#')
    print('#'*64 + '\n')
    for d in commands:
        print(d)
    confirmation = input("Please Confirm[y/Y/yes/Yes]: ")
    if confirmation.lower() in ['y', 'yes']:
        pass
    else:
        print('\n\n\n\nCONFIRMATION NOT PROVIDED!!! THEREFORE EXITING THE SCRIPT!!!\n\n\n\n')
        exit()
    return commands


def adhoc_commands_exec(ipd,ip,hostname,pwd,commands,adm_opt):
    commands = commands
    try:
        ip = ip
        hostname = hostname
        pwd = pwd # type: ignore
        connection = connect_device(ip, pwd, adm_opt)
        host_out_str = ''
        pr_len = '| ' + \
            '{:^60}'.format('connecting to device - %s') % hostname + ' |'
        host_out_str+='\n' + '#'*len(pr_len) + '\n' + '-'*len(pr_len)
        host_out_str+='\n' + '| ' + '{:^60}'.format('connecting to device - %s') %hostname + ' |'
        host_out_str+='\n' + '-'*len(pr_len) + '\n'
        print('-'*len(pr_len))
        print('| ' + '{:^60}'.format('connecting to device - %s') %
                hostname + ' |')
        print('-'*len(pr_len))
        #to get commands
        for command in commands:
            result = connection.send_command(command)
            host_out_str+='~'*50 + '\n'
            host_out_str+=command + '\n'
            host_out_str+='~'*50 + '\n'
            host_out_str+=result
            host_out_str+='\n'
        connection.disconnect()
        ipd[hostname] = host_out_str

    except netmiko_exceptions as e:
        print('failed to ', ip, e)

def manager(df1, commands, adm_opt):
    """Manage parallel execution of commands on multiple devices"""
    with Manager() as manager:
        ipd = manager.dict()
        with Pool(min(cpu_count(), len(df1))) as pool:  # Limit pool size
            tasks = []
            for i in range(len(df1)):
                ip = df1.loc[i]['ip']
                hostname = df1.loc[i]['hostname']
                pwd = get_sec((df1.loc[i]['pwd']).encode('utf-8'))  # type: ignore
                tasks.append(pool.apply_async(
                    adhoc_commands_exec, 
                    args=(ipd, ip, hostname, pwd, commands, adm_opt)
                ))
            
            # Wait for all tasks to complete
            for task in tasks:
                task.get()
                
        return dict(ipd)

if __name__ == '__main__':
    adm_opt = get_command_option()
    if adm_opt == '1':
        commands = get_commands_dombyp()
    elif adm_opt == '2':
        commands = get_commands_domsslbyp()
    elif adm_opt == '3':
        commands = get_commands_ipbyp()
    elif adm_opt =='4':
        commands = get_commands_probip6drp()
    elif adm_opt =='5':
        commands = get_commands()
    else:
        exit()
    wco_out = manager(input_df, commands, adm_opt)
    # print(wco_out)
    commands_string = '\n'.join(commands)
    # print(commands)
    # print(commands_string)
    for i in wco_out:
        with open(logfile, 'a') as f:
            f.write(wco_out[i])
    for i in wco_out:
        with open(temp_logfile, 'a') as f:
            f.write(wco_out[i])
    sendMail('<EMAIL>', ['<EMAIL>'], 'SIE WCO Changes: '+day, 'Hi,\nThe user ' + user_admin + ' has issued below commands to the WCO devices...\n\n\n\n' + commands_string + '\n\n\n\nAlso review the attached logs for reference\n\n\n\nThank You.', [temp_logfile])
    print('Done')
    exit()
