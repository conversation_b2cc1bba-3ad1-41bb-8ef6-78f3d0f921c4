#!/usr/bin/env python3

import os
import sys
import base64
import time
import netmiko
import pandas as pd # type: ignore
from base64 import b64encode as b64e
from base64 import b64decode as b64d

from netmiko.f5.f5_tmsh_ssh import F5TmshSSH as f5t
from netmiko.f5.f5_linux_ssh import F5LinuxSSH as f5l


netmiko_exceptions = (netmiko.exceptions.NetMikoTimeoutException,
                      netmiko.exceptions.NetMikoAuthenticationException,
                      IOError)

basedir = os.path.dirname(os.path.abspath(__file__))
outdir = basedir + '/output'
inpdir = basedir + '/input'
input_csv = inpdir + '/cyberark_out_secrets.csv'
input_df = pd.read_csv(input_csv)
day = time.strftime('%b_%d_%y')
outpute_file_name = '/ad_hoc_f5_%s.txt' % day
logfile = outdir + outpute_file_name
if not os.path.exists(outdir):
    os.makedirs(outdir)
sec_path = b'L2hvbWUvcHNvbWFzMTc4L3NpZV9wcm9qZWN0cy9zaWVfd2NvL2lucHV0'
secr_path = b64d(sec_path).decode('utf-8')
sys.path.insert(1, secr_path)
from sie_get_secrets import * # type: ignore
u = get_sec(f5_usr) # type: ignore

def connect_device(ip, p):
    x = f5l(ip=ip, username=u, password=p)
    # x = f5t(ip=ip, username=u, password=p)
    return x


def get_commands():
    commands = []
    print('#'*64 + '\n')
    print('Enter the commands to be executed...\n')
    while True:
        command = input("Command: ")
        if command == '':
            break
        else:
            commands.append(command)
    print('\n' + '#'*64)
    print('#' + '{:^62}'.format('Commands to be executed...') + '#')
    print('#'*64 + '\n')
    for d in commands:
        print(d)
    return commands


def adhoc_commands_exec(df):
    commands = get_commands()
    for i in range(len(df)):
        try:
            ip = df.loc[i]['ip']
            hostname = df.loc[i]['hostname']
            pwd = get_sec((df.loc[i]['pwd']).encode('utf-8')) # type: ignore
            connection = connect_device(ip, pwd)
            pr_len = '| ' + \
                '{:^60}'.format('connecting to device - %s') % hostname + ' |'
            print('-'*len(pr_len))
            print('| ' + '{:^60}'.format('connecting to device - %s') %
                  hostname + ' |')
            print('-'*len(pr_len))
            with open(logfile, 'a') as f:
                f.write('\n' + '#'*len(pr_len) + '\n' + '-'*len(pr_len))
                f.write('\n' + '| ' + '{:^60}'.format('connecting to device - %s') %
                        hostname + ' |')
                f.write('\n' + '-'*len(pr_len) + '\n')
            #to get commands
            for command in commands:
                result = connection.send_command(command)
                with open(logfile, 'a') as f:
                    f.write('~'*50 + '\n')
                    f.write(command + '\n')
                    f.write('~'*50 + '\n')
                    f.write(result)
                    f.write('\n')
            connection.disconnect()

        except netmiko_exceptions as e:
            print('failed to ', ip, e)


if __name__ == '__main__':
    adhoc_commands_exec(input_df)
    exit()