
######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bearcreek.tx.hou.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


########################################################################################################
--------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.hillsboro.or.ndchlsbr.comcast.net                   |
--------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.potomac.co.ndcwest.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal domain_contains | grep acompli
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bearcreek.tx.hou.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

########################################################################################################
--------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.hillsboro.or.ndchlsbr.comcast.net                   |
--------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.potomac.co.ndcwest.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { acompli.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { msftauthimages.net }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { omniroot.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { public-trust.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { yammerusercontent.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { prod.entos.sky }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bearcreek.tx.hou.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

########################################################################################################
--------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.hillsboro.or.ndchlsbr.comcast.net                   |
--------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.potomac.co.ndcwest.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*c-cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*c-cdsknn.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*ugwdevice.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*ugwdevice.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cdsknn.net.edgesuite.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cpan.org/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
