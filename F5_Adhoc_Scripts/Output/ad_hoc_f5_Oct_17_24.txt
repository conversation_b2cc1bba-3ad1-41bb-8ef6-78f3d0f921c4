
######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bearcreek.tx.hou.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

########################################################################################################
--------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.hillsboro.or.ndchlsbr.comcast.net                   |
--------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*nt-ware.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
root@(ccswco01)(cfg-sync Standalone)(Active)(/Common)(tmos)# modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bearcreek.tx.hou.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.

########################################################################################################
--------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.hillsboro.or.ndchlsbr.comcast.net                   |
--------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.potomac.co.ndcwest.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*about.me/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*about.me/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*agora.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*agora.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*alarmnet.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*alarmnet.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*apzones.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*apzones.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bluvector.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bluvector.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*bugsnag.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*bugsnag.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudflare.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudflare.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*crestron.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*crestron.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cvent.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cvent.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*databee.buzz/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*databee.buzz/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*emc.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*emc.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*evertz.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*evertz.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*firebaseio.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*firebaseio.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*gamespot.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*gamespot.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*hubapi.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*hubapi.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*intercom.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*intercom.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*juniper.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*juniper.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*kissflow.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*kissflow.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestream.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestream.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*livestreamstudio.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*livestreamstudio.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*mistsys.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*mistsys.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*remirepo.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*remirepo.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*riverside.fm/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*riverside.fm/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*sourceclear.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*sourceclear.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stream-io-api.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stream-io-api.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.com/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*stripe.network/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*stripe.network/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*veracode.us/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*veracode.us/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloudfront.net/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*cloudfront.net/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*xpki.io/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*xpki.io/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*icloud.com.cn/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass https://*icloud.com.cn/) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*cloud.elastic.co/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { https://\*elastic-cloud.com/ { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls delete { https://\*a01.ugwdevice.net/ }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020036:3: The requested custom category URL (/Common/ccs_custombypass https://*a01.ugwdevice.net/) was not found.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bearcreek.tx.hou.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

########################################################################################################
--------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.hillsboro.or.ndchlsbr.comcast.net                   |
--------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.potomac.co.ndcwest.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { treering.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with treering.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
