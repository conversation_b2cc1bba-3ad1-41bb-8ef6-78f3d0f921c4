
######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------

###########################################################################################
-------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mtlab.nj.comcast.net                   |
-------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


###########################################################################################
-------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.mtlab.nj.comcast.net                   |
-------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01070712:3: Values (/Common/ccs_custombypass) specified for custom category URL (/Common/ccs_custombypass *nutanix.com*): foreign key index (url_category_FK) do not point at an item that exists in the database.

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested custom category URL (/Common/ccs_custombypass *nutanix.com*) already exists in partition Common.

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.tidwell.tx.hou.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.stonemtn.ga.bgsth.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
modify sys url-db url-category ccs_custombypass urls add { \*nutanix.com\* { type glob-match } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with windowsazure.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.tidwell.tx.hou.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with windowsazure.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with windowsazure.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.stonemtn.ga.bgsth.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { windowsazure.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
01020066:3: The requested class string item (/Common/domain_ends_with 911.west.com) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.tidwell.tx.hou.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.stonemtn.ga.bgsth.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { 911.west.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.tidwell.tx.hou.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.stonemtn.ga.bgsth.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { counterpath.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { softphone.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 5
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 15
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { facebook-http-mon facebook-https-mon gateway_icmp yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon }
    service-down-action reselect
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 5
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 15
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { facebook-http-mon facebook-https-mon gateway_icmp yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon }
    service-down-action reselect
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address **********
            description "GRE Member1"
            priority-group 15
            session monitor-enabled
            state up
        }
        Chicago:any {
            address **********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address **********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { gateway_icmp facebook-http-mon facebook-https-mon yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon }
    service-down-action reselect
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address **********
            description "GRE Member1"
            priority-group 15
            session monitor-enabled
            state up
        }
        Chicago:any {
            address **********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address **********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { facebook-http-mon facebook-https-mon gateway_icmp yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon }
    service-down-action reselect
}

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 10
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 15
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { gateway_icmp facebook-http-mon facebook-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon yahoo-http-mon yahoo-https-mon }
    service-down-action reselect
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 15
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { cnn-http-mon cnn-https-mon facebook-http-mon facebook-https-mon foxnews-http-mon foxnews-https-mon gateway_icmp yahoo-http-mon yahoo-https-mon }
    service-down-action reselect
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 15
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { facebook-http-mon facebook-https-mon gateway_icmp yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon }
    service-down-action reselect
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 15
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { gateway_icmp facebook-http-mon facebook-https-mon yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon }
    service-down-action reselect
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address **********
            description "GRE Member1"
            priority-group 10
            session monitor-enabled
            state up
        }
        Chicago:any {
            address **********
            description "GRE Member2"
            priority-group 15
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address **********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { cnn-http-mon cnn-https-mon facebook-http-mon facebook-https-mon foxnews-http-mon gateway_icmp yahoo-http-mon yahoo-https-mon foxnews-https-mon }
    service-down-action reselect
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 10
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 15
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { gateway_icmp facebook-http-mon facebook-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon yahoo-http-mon yahoo-https-mon }
    service-down-action reselect
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 10
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 15
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { facebook-http-mon facebook-https-mon gateway_icmp yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon }
    service-down-action reselect
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 5
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 15
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { cnn-http-mon cnn-https-mon facebook-http-mon facebook-https-mon foxnews-http-mon foxnews-https-mon gateway_icmp yahoo-http-mon yahoo-https-mon }
    service-down-action reselect
}

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address **********
            description "GRE Member1"
            priority-group 5
            session monitor-enabled
            state up
        }
        Chicago:any {
            address **********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address **********
            description "GRE Member3"
            priority-group 15
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { facebook-http-mon facebook-https-mon gateway_icmp yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon }
    service-down-action reselect
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 10
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 15
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { gateway_icmp facebook-http-mon facebook-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon yahoo-http-mon yahoo-https-mon }
    service-down-action reselect
}

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 5
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 15
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { gateway_icmp facebook-http-mon facebook-https-mon yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon }
    service-down-action reselect
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address **********
            description "GRE Member1"
            priority-group 10
            session monitor-enabled
            state up
        }
        Chicago:any {
            address **********
            description "GRE Member2"
            priority-group 15
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address **********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { facebook-http-mon facebook-https-mon gateway_icmp yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon }
    service-down-action reselect
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.tidwell.tx.hou.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address **********
            description "GRE Member1"
            priority-group 10
            session monitor-enabled
            state up
        }
        Chicago:any {
            address **********
            description "GRE Member2"
            priority-group 15
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address **********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { facebook-http-mon facebook-https-mon gateway_icmp yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon }
    service-down-action reselect
}

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 10
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 15
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { facebook-http-mon facebook-https-mon gateway_icmp yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon }
    service-down-action reselect
}

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 15
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { facebook-http-mon facebook-https-mon gateway_icmp yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon }
    service-down-action reselect
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address **********
            description "GRE Member1"
            priority-group 15
            session monitor-enabled
            state up
        }
        Chicago:any {
            address **********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address **********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { facebook-http-mon facebook-https-mon gateway_icmp yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon }
    service-down-action reselect
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 15
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { gateway_icmp facebook-http-mon facebook-https-mon yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon }
    service-down-action reselect
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 15
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { gateway_icmp facebook-http-mon facebook-https-mon yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon }
    service-down-action reselect
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 15
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { gateway_icmp facebook-http-mon facebook-https-mon yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon }
    service-down-action reselect
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address ***********
            description "GRE Member1"
            priority-group 15
            session monitor-enabled
            state up
        }
        Chicago:any {
            address ***********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address ***********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { gateway_icmp facebook-http-mon facebook-https-mon yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon }
    service-down-action reselect
}

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.stonemtn.ga.bgsth.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm pool gre-pool_ipv4
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm pool gre-pool_ipv4 {
    members {
        Ashburn:any {
            address **********
            description "GRE Member1"
            priority-group 15
            session monitor-enabled
            state up
        }
        Chicago:any {
            address **********
            description "GRE Member2"
            priority-group 10
            session monitor-enabled
            state up
        }
        Hillsboro:any {
            address **********
            description "GRE Member3"
            priority-group 5
            session monitor-enabled
            state up
        }
    }
    min-active-members 1
    monitor min 5 of { gateway_icmp facebook-http-mon facebook-https-mon yahoo-http-mon yahoo-https-mon cnn-http-mon cnn-https-mon foxnews-http-mon foxnews-https-mon }
    service-down-action reselect
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.tidwell.tx.hou.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.stonemtn.ga.bgsth.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-down session user-disabled } Hillsboro:any { state user-down session user-disabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.tidwell.tx.hou.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~


####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.stonemtn.ga.bgsth.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm pool gre-pool_ipv4 members modify { Chicago:any { state user-up session user-enabled } Hillsboro:any { state user-up session user-enabled } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

