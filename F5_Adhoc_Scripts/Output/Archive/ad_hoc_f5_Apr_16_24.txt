
######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    10.164.1.165 { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    10.164.1.165 { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bearcreek.tx.hou.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    10.1.129.76 { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    10.1.129.76 { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    1********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    1********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 ************/23 ************/23 ************/23 ***********/23 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    10.24.174.150 { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    10.24.174.150 { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    10.172.253.147 { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    10.172.253.147 { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    10.189.69.161 { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    10.189.69.161 { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    10.36.253.152 { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    10.36.253.152 { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

########################################################################################################
--------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.hillsboro.or.ndchlsbr.comcast.net                   |
--------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 *************/31 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys sshd allow
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys sshd {
    allow { ***********/16 10.0.0.0/8 *************/27 *************/28 ************/32 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 ***********/16 **********/12 *************/28 **************/29 *************/31 ************/23 ************/23 ************/23 ***********/23 }
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    *************/31 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bearcreek.tx.hou.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

########################################################################################################
--------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.hillsboro.or.ndchlsbr.comcast.net                   |
--------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys sshd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify sys httpd allow add { **************/29 }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify security firewall management-ip-rules rules modify { https_allow { source { addresses add { **************/29 } } } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf
