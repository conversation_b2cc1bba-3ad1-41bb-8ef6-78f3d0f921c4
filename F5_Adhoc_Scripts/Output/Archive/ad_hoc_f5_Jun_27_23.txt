
######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include none
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include none
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.tidwell.tx.hou.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include none
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include none
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.stonemtn.ga.bgsth.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include "<If \"%{HTTP:connection} =~ /close/i \">
    RequestHeader set connection close
    </If>
    <ElseIf \"%{HTTP:connection} =~ /keep-alive/i \">
    RequestHeader set connection keep-alive
    </ElseIf>
    <Else>
        RequestHeader set connection close
        </Else>"
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ALL:!ADH:!EXPORT:!eNULL:!MD5:!DES:RC4-SHA:!SSLv2:-TLSv1:-SSLv3:-TLSv1.1
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include none
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-SHA:ECDHE-ECDSA-AES256-SHA:ECDHE-ECDSA-AES128-SHA256:ECDHE-ECDSA-AES256-SHA384:AES128-GCM-SHA256:AES256-GCM-SHA384:AES128-SHA:AES256-SHA:AES128-SHA256:AES256-SHA256
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}

########################################################################################################
--------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.hillsboro.or.ndchlsbr.comcast.net                   |
--------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list sys httpd all-properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
sys httpd {
    allow { 10.0.0.0/********* *************/*************** *************/*************** ************/*************** 2001:558::/33 2001:558:FC00::/39 2001:558:FE00::/39 **********/12 *************/28 **************/29 }
    auth-name BIG-IP
    auth-pam-dashboard-timeout off
    auth-pam-idle-timeout 1200
    auth-pam-validate-ip on
    description none
    fastcgi-timeout 300
    fips-cipher-version 0
    hostname-lookup off
    include none
    log-level warn
    max-clients 10
    redirect-http-to-https disabled
    request-body-max-timeout 0
    request-body-min-rate 500
    request-body-timeout 60
    request-header-max-timeout 40
    request-header-min-rate 500
    request-header-timeout 20
    ssl-ca-cert-file none
    ssl-certchainfile none
    ssl-certfile /etc/httpd/conf/ssl.crt/server.crt
    ssl-certkeyfile /etc/httpd/conf/ssl.key/server.key
    ssl-ciphersuite ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-ECDSA-AES128-SHA:ECDHE-ECDSA-AES256-SHA:ECDHE-ECDSA-AES128-SHA256:ECDHE-ECDSA-AES256-SHA384:AES128-GCM-SHA256:AES256-GCM-SHA384:AES128-SHA:AES256-SHA:AES128-SHA256:AES256-SHA256
    ssl-include none
    ssl-ocsp-default-responder http://127.0.0.1
    ssl-ocsp-enable off
    ssl-ocsp-override-responder off
    ssl-ocsp-responder-timeout 300
    ssl-ocsp-response-max-age -1
    ssl-ocsp-response-time-skew 300
    ssl-port 443
    ssl-protocol "all -SSLv2 -SSLv3 -TLSv1"
    ssl-verify-client no
    ssl-verify-depth 10
}
