
######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bearcreek.tx.hou.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

########################################################################################################
--------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.hillsboro.or.ndchlsbr.comcast.net                   |
--------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { bluvector.io }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$eZLb29w1$TmHZyxbxXCsdweExpv1CdTyYkB2RSeoOzXKI6KBjOq1dXDHo5CcL3sW9qCbyjkVcLvcoPqQeuGAE2M4RcsvsP.
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell bash
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$5m95Z1DF$6vTV.7RkVBjJvpRAwOle9BrBP./vdyJbvGn2tlajk9CZTJmBJOiShnes/BjsZqah79pZLkXODavP23Y8uTpz6/
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$pHV/TNwv$P7WqRG8jazfP8xDwuokyOmrlH40n6s.RTOpSRKJQX7rVffiDksc5QhyFU9H1iYFk/kx0KzjxvrKtjtNknaYQY/
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$ty.9RHvk$YDgUnpeI3yf9aM9VuwClUiah0eDAa66juW.MKPAiKL8JCQkBFO.Z5slzo7RaIEY3PUYP.HmPwi/EAQQ1a/IME1
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$3K0gIQh/$.wFqyE8c1cilaH5TUdKZoxxvET.Svxxk/XUcg6iOg70IBox71qp90rjdO5kuOzrxaCx8u8qsmmFc2ELWFdgDg/
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$KeSH4QNF$NiyiZH2LMhfhOuO3gSAxro2vOecb0J9U9TkeY9nT9iqAXgEXUP9SMM.tUrOQWsJx7NMzI1831pvZbvxy5EMOf0
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$HmuU72xU$iMRUEahD0z2gUj2ybZO.GXOo8uNP5tB/c5hw7yYrgOv7InwCETXeNZ52afSS5iLqBoAfvYBgDzo34bG4AS9Mp.
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$uVkf3/Gw$8ylBTvu0vjMxs1md1rtbZxV9LDoEFVnFb1ejiuL0YCZFGM6FwSECyTlsGeHVOHRywWojPivEMB6cvirEFr7Ao1
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$dFz2XbmV$TiguFZN1Q.sZVp8JusanX3DiIMQhaGUgQ/FEOQyoSSj2mKOQDj90lVDoIklcmcgqI9VFKzVfxeCL6X0zvRRj//
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$BT1iJ4yG$IIoVI1ZsH9sKEzvHiCCTv/DMk.Q2O5lQ656wq2TGWkiYi8dIcuIFioMuk4tadp.Lgn8SeHnH2eoujTklyzmat.
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$7FKOhVWw$czBng5LQxYpOoSEh5gya.KGFA7c2CQiFgEx8e/sWbKSkZ9iGqs2FR0UIIFmN1RjxzF3PV4F4kp5a0AcgzPGn./
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$u1FT5MV/$lPjKd.Th4YfXPGw8VPTL590jaOzc0K52mCDHzExhRk9CDSX7sHPr60eYSIrZd3lLzFE0JNHlQ5vgyj6ASJvKI/
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$onHYa.tQ$cDhzqJLgIKeFigC43BWm0FG46BAnbW3gHZQJstPadxz/vk0MNMxa/CyshcKr9SokcnJSCFQW0gbKsBJk9sIwl/
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$PtYQTJ4T$C4k4yGkqyTJ2NB47/m6hfE/wKKOd9.kZDPRTyAGVeAzJ17Df9f7lnSZlSUHSwbjLFdUBzX8rL0OZ2ncn524z10
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$iy1K7lKI$HW9XdkLe9NWwcNHwwdl4CelnwfgX5ZBDTWobRyJVnK4Q0wemp6oysIsgqXyJtuT9i92V7VsP.EcT5DDJGgOCU0
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$QeVZ0dNl$u3Ux75/RfDgmsIOmksd9Bhc2JsvLhzFT/pfoWzGBjwdr0yY.HAFymS/xbMfXeO06wBY.erLBDZ9vMdigoh3OM1
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$O.9pDTkq$H35Fu8OPgKRl4ifih79SavWE8OS2q6zOHg2AXU1lPAfvVkco6WkdGoT3g3igJkwN5d4cXDBK31IR5ErUmMUMQ0
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$3oZIYKYR$AIhUZqlTt6DOqy0zDk5q0sf90/MGj9s0SZVug4Kd6Sp3h1JzCxTMq.IK1w1t65jPXTK.rg7RanuG8u.zyl8ih.
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$6DVlwhSP$nVe7NcJrYcB2F6blK/iw1s/jgfI7WK1xUPgvvfbQpnMgCeTp/inBcOuxoem49O5FCz6A1hL4DjbW6WATATB/80
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$yCcYYs4Q$6YM0Q5T6iGw4G8gFVS6YVyOoHVjv97JfCrBxF/BzKHXSTRTHruS7GYlPp/PlSGxqX0DELLDso2gJEVomQ47bp0
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$uSw1w0ZC$VSqDL3ZOYClmWYOGkq70BOU4D7ynxfK.//btrj8wNJaQnOz14c6LT8lpjkQZcSr2CCntkGv3/eOEN.huoBoN/0
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bearcreek.tx.hou.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$IT4vMaa7$4c/6bS/NPjzvZpywey8.Tn27kuNnPKW8mJWLlLcanPtfSLK8SlrpRKxz18zVblAwhbonfbLBDHy8wBeAL1oxo1
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$le/2e.e0$URT2CSeymVTFlmdsNfjzo5aOYy6cy8ATw5g1IWBMKnGZP5Qip5qo9DgqrisNbiyRHTsAUWXAulOiQA7StfND/.
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$laNJZXUl$jmXarrXJsM4lIj0dPzaiQVWADA82THsJquBAZsoxMOoFTLETkKavEYm4EYt/E4.SAKEY1.vSjVPyRlWlhncNi.
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$Oh4D4IgZ$tokwThg12Yf80CvO8E2G9om.jRfB8ejLLO3le7EXqYcETdjSlJWllhegxnp4AcBHPeHJxfjC7dKD3pRW8SKV2.
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$3k5147IB$knhMfP5P9lKKW6iI6mzoT/hBO/T41LLKohL8KOqDB.rmX1MZUgeGlZ7r6xC7gK/vyanxpGxXRqm5EPGj2WloG1
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$7ZiEyi5c$0dT.uAKpS2y7tZ3QqphXNP/1mXi4lPKuje6vqUFV9OjKZw6ACJ23iMZA0g8M34wCjlW6eOyp.hcgY7Y.z6zr81
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$oGAYI7Mz$CDNIA64QRO9BXZbwR7bkuBD7s1mFJIAYHLdMZQRhjTS44JlHN1V6SQR9Smypr6h.JhhdIdhl/60TOs6uj5aVG.
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$7iKa09yA$JuK2w/.XGs29TwPcTFb7.KAnQuH73Cu.OECBHUpnwqkzTqMK0MSCYQyG.WPYdCEkNFJjOwekH5kSTWPRc.DOp1
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$AhldyhBl$.aC9SEW8y.EbgjjD/U.Xqs4jAZYAP3.ZWiQLear2wJhh/uPrLtOxzbHGqp7Wa6cREoGxjNUisgSe1.VhNLOZf0
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$sNPBbNWL$nifzFmcYavZgscrfi7r6uTcl1ofsvNsN5EiVb5v5lhVIe0UDbEH7SwkcMsD5RJkSsbendgeSpvehZzvsGV0e//
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$aQvyof0v$uvTnbWfMTnXwatGNZlwHFlX6ewGa7S1RA3sjJ22YFAikAYi7ZcKrs//P.6Pk/O9CHBri1YifBtlLaPPvIV2dd/
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$ZqS./urG$kx7mKLgGnzhTCAwwDwz5jeqnRhZFPh/hO4yjYUuqEFX0uIfmiNBRqQcgnzop37LeGw7Dnw8nfZABmy9TYeN3j/
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$6npfhNEe$xH6sZAB5gvmENbAoV2kQ/yGzAO3KxIJcbI7CMY7VAuGUs4EfaldWJCSl1rHSHzvufw3BADevhLJal7px.1Clg1
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$g8b4BohX$hkGE47m51Zj4lp8AjeDR2.jatP/RTOAYj60IUkXALRXk4.MBphfSKO0vOHskHY4rGVw4VLIDmQo8l6.unuiJ80
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$OL7KnJMB$MIlnNjS7eRbe/OPC1qrxVvXouvkpYwkv/C08PQLv2ReygLyeJFSRjI1IcWt609RQUEokKNukt0xh.vf6SqKow0
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$98hMHKXo$T/8.gw6FeF4O6qUG4bvAmBShS9jEeYSNrW.uqA3lbwBpEIVp4XkgH8O3cUH2UNPi2ahm3U2QCoCpLbo7nZonN.
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$0LHNVf67$N/XB1c3SB0NwxF9shbKovyTooRQYxpRe8KGEnAWrn0A.P43XeQfUEKS6/aZ18w2OEu68kV7T1aoSl8aRLl3pv/
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$F3kPAk/A$HjaKto8An3i91sdlfDPIfv7uJOD.f9HL5rm/Hw154iW06Ko/EHPtEmOghTMedEsceJ9AmHV0aziydPl6Cb/Ke1
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$9rqcdlh2$sTa7Dv4MwtVPH20r2H8V5GlttQDMzQYHBQIK9XwjR.j41exEpSh8JtLGzhVDORuozE/2Gd7UHIKTuS.BMX.P11
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$kFMcxie5$JQ3xGUxImzwXQfynAxAmPlrpcPLDOjUI54SdCSs5I92xdiXr6P0dNi4D5x2UMpyIoDNvKuEWn6C3akecIgDNO/
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$6q/vdAXg$Jf2fPNu8HCSWWFAZWRLd0JcJN5xGrEdhfiggYF22UTvhtxXQAFlx9CiEH31iJgLszUKf/CmlT8hyok/D5ZSqP/
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$s5J0ySi2$my7BpwvMMqlIplDV8n9CkU1j2eTfs99WG12Cho6h9XjpfKpAwEHbY1RTMZpDQqXdN4FQIU41Oy5QLBlVjAxb6/
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$k5ufewZL$MehwBMrtATrmQApLyH/mhmmG7iBwTMpStmQG/eywgwAibW2O.6nTWwaxmEtfqr0mOeAuIjw1IhTyEknzCv4yl.
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$hsTdgRfn$pLqmSyApgSOkSg/Hi1mMzAjYK9ujoqoaPXx7fvhGTbKsa6CowZzkGixZV/OtwkmsC3dMDRhiMg.4nAf1U1SJS.
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$g7El1mfe$S/ndIXJORSbOHBNzaQc6u/HnjCZ492UBIr9/cKXr2cGnNFQ.HvNIFCuYFRw/YqEfjAbGja/PAsFnd6R5OK4tR1
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$zmYzPc3a$MA.j8Wk9hrGeX3yh630EGl2eI8/4pzPtDGl6FzwY6GyVxdQ10WMmnllEhz9sy6k2lRmOx5FJKaX07fGAGdddJ1
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$RgHvzKww$TCBAPp/L7pP8Wn9ka4YET34VbeSg7rN.wXaEsz0Y4MHtA5Gy4W1YccUNEAReMv6ML.jeK7I/nCV73vhQgnX5W/
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$71KcSFjS$ZM4ARZ9faCUsaGGnvI4ybER4SsWMTf0rgbM/YC3PHQkM72eBp7uiPY6oQV.qvNBaP1DlP9j8XiBfS4AKllO0r.
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$S0c3Kyco$OQrmjmWJRpbfTKnwgootHqoC7KfquAY/CBghTSHapAgbmxxow1.8G/RUovuP/XveiKYKEvF5His5jvbn.fHGN0
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$6CO/6K8z$LNl5yHIYm0VJy6U.Vml.3yKypFORgElwAAZ28yyqPoAR4BPvHEqLP2BpqSvYc.iUnU6HkrmgLkrhBcK4DbXWn0
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$M2M4XYP4$UUNm4OV2JbqBIXWSXGBpLaPpgcBBCV8gr0zQQSydPNghTrRTGIo.HBz5OLiWRSLI3fUbLptHP/BLOxyKn/RHX1
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$DZLKfqem$aDdBmQegSt36ImsmcYiqepQu618j/W41KsS1IyiYw5EHmsol5sQVwuRAYrmsafxQJX5.5nBV0TVRH0m0YvWIm/
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$fh3wuFER$siUdiKFF43nHJ.TOvTaugw2nNUDxjgLUTkGE.UquMB6WyeIJ4gBW9qCZ6sOrhPTMxmOF.NHE9OizvVkehsdeu.
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$IdKXqENi$SFsBY8w4vsS1cfdaCBFEn4xivVSUt2dPo7ugV6dgDgLkjwUgWN3yLMXerG5j0JI5oOQ.5cngaAYUoqe2k9pKa1
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$jnjWSVtR$Nf37wunLyEC88hBFLgFlsh0QqgCLS.9chqe0YL2gN/FvW0vV5RU3KKNWtNvg/WZW.12iV4EVU11Q.P7AcNapZ1
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$j3Ya.KHS$QaxtjnQ8mDKiNFYHE7dqVXsPw0FMMovzwqAw1UkxzFSPlNYWmOwTJKtRngytPTH9QFIzkhFg.UhDAGTEArDu0/
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$0.5F1Tia$N1bzw/33MXCW2Q73T/OZxv68wpvhIPUUYhQcfQbGN4SKczzPYTzFWZ3WRrhySpEuh4cpZMUHPUJMmhnfUpdoY0
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$NBsIF3nn$Hr6Hl8pThar2/grmtREpyfqq9XyaJuLrVxcQ3DTadduutvzgMDv7MZuyD.PYQ0P5xf510L0FbJil8yUJ9/xYu/
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$0X/U4wmD$oeW3uAs/MJVywo59Nt3C15ArAoiPFGKLZktLXuIfx6zTNlhkldDuV4z5MD9aKl5XU5ca78W80YlDWSn0vrep//
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$bAwD6f4B$xnvKwbFbvK/D4SPLs4t8LCO3/rXZylXGYbqvDY.njo180LELwZA88Wch0DVZpbLUxYWaif2xuIsBq/colGCgF0
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$wz7HCTTq$gy1mv//69MAJ.32wHEjTbykdPF/TGVK.nrS/FVNVhr7Zcz6t/Wbr9IDMhEnWSeE4cCqfIYhVuoNCIAgY2Zq3F.
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$FNy2FLy7$8YGMYpdbZ9Bd7qusOrT1AgP31MBx1pr7Ucty4g8Va9ag761n.zMh9RG0pLt0ciFClns3m8eFh.AYeMr5cF1271
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$7MB8fj0k$i1wMMYruPKr2fxZdskrf16z5kb9Y.XEMpqceCPlCAwdw9g0aqnMRQvBx7J0XP3iJ44uYg2w7wkdJoYA0XS3E8/
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$YmT.7K1P$LMnWkrko8xbXFjC4gb27Sstkypg//2uo/SYETsz4n/FftjKPNTp0w0xMbb5Ui.SAmiWN5VZ39L8Nw..3cqBZ5/
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$2BMgWOhk$tMHPl57fKUcB4Cfo2fC.rxVZkpD5jzExtGIDiNv7Y5XaxKlYhhdMQEelKVKW1TnPgZ.ihhU5xzOycfceE455w1
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$B6GqSQCf$w0TbB2xfx3pic7380fl.HCaTv8L.EraNvngArP3irxZPY4mMPgQDtOuDHd/L9GnpPoGkMA9xw80/caODAFMQW.
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$mtIzEPjz$PBNva5InArn9N9XpwM6.Yc9q/LFFv.Av7b/w3k.8aBMvk2ym.gll2tYifNljjuGPt5HimukXPZHPVlujqRaay/
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$eK3q5NLz$BnzN0oKvbyx8CfGuLh0VHeXNrGNfdcGjD8TddRkqoo3ZPa3n9IuD6PVDr8CRuVUi/ipwxBcTizTmn424eDa2U1
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$T9Z.QpfZ$jwmerL11vflUwSAgbrUR.FkI49GwsKYr6Ao0Tf4UP8NueTU1/WjBZcwAJbCwbmnbmiuwk1xtXepB5LNn9hRSn.
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$pT7H/TfI$TzfRjiidx81yeEUFqCCmIbkJpQNnT1XGUJgi7OF/d.D8JEV9On5dxe5eZsKw3YE2/I6bAY2ixdItn9mc23Ny1.
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$hptXkJ58$Xpps9VBcrAROSFscm9FRWBd2tVFVSzCAifohYzHPNqSvkfwlw7Z3Z.5rMCNJNVabNgEkJWuC5q77Gr4saebVP0
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    shell none
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$cwe/jngq$plGdiDb7YbfdeI.QA/UGgeEOPUZuBfhr8KG821Dv2muTfSP2cQ5grAT7t075/uvw8xlAG3E04a62D4lkA1CFu0
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$cfhNF9e0$ahFUO72P08oQEm8drZHF5lcb1rIQb9x7KIeuar3Pl0URg3cIiwLRW3Zu2VXTeUawI3aaZccW7Sz7cM.hJAKMn.
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$1t695BDu$v4HcLlwojSUWW9GYmDgP5UbooSeKgKLnKDIgzwiYXPOCZd2rHgPKup9gOa0ClL6xwhLzn9X1Sbhx4WIVJz6iK.
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}

########################################################################################################
--------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.hillsboro.or.ndchlsbr.comcast.net                   |
--------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list auth user
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
auth user admin {
    description "Admin User"
    encrypted-password $6$80Ocg9Bv$W7HGOnlTz8hf5OGuMX3BXzfGE4rX5V/fj5cojc70adSaUJL.LqR/3lJKaD3n563Hxl5njxoJGKhqY.z5yl58H/
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
auth user siemon {
    description "SIE Monitoring"
    encrypted-password $6$80P5JFiU$SIC6kLip3BQApFdFyakZRpprpJemD7eb9EgAXALp7B2e/QNE0IyeagicJ5uZmZVaNvapgvAeMCLLalb05Nk/U/
    partition Common
    partition-access {
        all-partitions {
            role operator
        }
    }
    session-limit -1
    shell none
}
auth user svc_tag_insight {
    description "TAG Insight URL-DB API Update"
    encrypted-password $6$ibW04reT$1LP6wO5XHum1hJINKtOOHoZy/C/.bY/QsUnnnCBec9zODKqeGxlxlPAUtx/V/ugum7WDFi2UcizLiMxyDHtJh/
    partition Common
    partition-access {
        all-partitions {
            role admin
        }
    }
    session-limit -1
    shell none
}
