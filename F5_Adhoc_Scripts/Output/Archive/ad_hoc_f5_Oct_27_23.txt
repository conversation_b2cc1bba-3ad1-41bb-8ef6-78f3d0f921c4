
######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bearcreek.tx.hou.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

########################################################################################################
--------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.hillsboro.or.ndchlsbr.comcast.net                   |
--------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.build }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfconnect.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.one }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfcloud.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamf.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfpro.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal domain_ends_with records add { jamfschool.com }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_script.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self serviceSelf-IP {
    address *************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self serviceSelf_IP {
    address **************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self serviceSelf_IP {
    address *************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Ashburn_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self Chicago_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Hillsboro_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self serviceSelf_IP {
    address *************/28
    allow-service {
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Hillsboro_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Chicago_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Ashburn_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self serviceSelf_IP {
    address ************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self serviceSelf_IP {
    address **************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bearcreek.tx.hou.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self serviceSelf_IP {
    address **************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
net self Hillsboro_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Chicago_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Ashburn_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self serviceSelf_IP {
    address ************/28
    allow-service {
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self serviceSelf_IP {
    address *************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self serviceSelf_IP {
    address *************/28
    allow-service {
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self serviceSelf_IP {
    address **************/26
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
net self Ashburn_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self Chicago_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Hillsboro_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self serviceSelf_IP {
    address *************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self serviceSelf_IP {
    address ************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self serviceSelf_IP {
    address ************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Ashburn_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self Chicago_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Hillsboro_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self serviceSelf_IP {
    address *************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self serviceSelf_IP {
    address **************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self serviceSelf_IP {
    address *************/28
    allow-service {
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Hillsboro_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Chicago_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Ashburn_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self serviceSelf_IP {
    address *************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self serviceSelf_IP {
    address **************/28
    allow-service {
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Ashburn_Tunnel-IP {
    address 1**********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self serviceSelf_IP {
    address ************/28
    allow-service {
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Hillsboro_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Chicago_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Ashburn_Tunnel-IP {
    address **********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self serviceSelf_IP {
    address *************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self serviceSelf_IP {
    address *************/28
    allow-service {
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self serviceSelf_IP {
    address *************/28
    allow-service {
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self serviceSelf_IP {
    address ************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self serviceSelf_IP {
    address ************/28
    allow-service {
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    *********** { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self serviceSelf_IP {
    address *************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************* { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}

########################################################################################################
--------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.hillsboro.or.ndchlsbr.comcast.net                   |
--------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list net self
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
net self Hillsboro_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Hillsboro_Tunnel
}
net self Chicago_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Chicago_Tunnel
}
net self Ashburn_Tunnel-IP {
    address ***********/30
    allow-service all
    traffic-group traffic-group-local-only
    vlan Ashburn_Tunnel
}
net self serviceSelf_IP {
    address *************/28
    allow-service {
        udp:domain
        gre:any
    }
    traffic-group traffic-group-local-only
    vlan service_egress
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list security firewall management-ip-rules
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
security firewall management-ip-rules {
    rules {
        https_allow {
            action accept
            ip-protocol tcp
            rule-number 1
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
            source {
                addresses {
                    10.0.0.0/8 { }
                    **************/29 { }
                    *************/28 { }
                    **************/29 { }
                    **********/12 { }
                }
            }
        }
        https_deny {
            action drop
            ip-protocol tcp
            rule-number 2
            destination {
                addresses {
                    ************ { }
                }
                ports {
                    https { }
                }
            }
        }
    }
}
