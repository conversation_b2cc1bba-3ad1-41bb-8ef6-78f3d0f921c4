
######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:18
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:18
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:19
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:19
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:20
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:20
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:35
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 111
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:36
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 112
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:36
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 113
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:37
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:37
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 106
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:37
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 107
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:52
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_995
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:53
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_871
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:53
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_426
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:53
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_995
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:54
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_871
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:54
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_426
    }
    vlans-enabled
    vs-index 7
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:08
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:08
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:09
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:09
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:09
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:10
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:24
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:24
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:24
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:25
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:25
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:26
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:40
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_887
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:40
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_625
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:40
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_479
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:40
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_887
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:41
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_625
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:41
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_479
    }
    vlans-enabled
    vs-index 7
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:55
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:55
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:55
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:56
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:56
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:56
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:13:48:10
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_636
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:13:48:10
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_410
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:13:48:10
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_282
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:13:48:11
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_636
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:13:48:11
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_410
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:13:48:11
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_282
    }
    vlans-enabled
    vs-index 7
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:25
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:26
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:26
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:26
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:27
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:27
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:41
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:42
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:42
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:42
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:43
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:43
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:57
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:58
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:58
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:59
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:59
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:59
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 7
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:14
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 111
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:14
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 112
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:15
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 113
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:15
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:15
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 106
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:16
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 107
}

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:31
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:31
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:32
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:32
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:32
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:33
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:48
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:48
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:48
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:49
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:49
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:49
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:04
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_571
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:04
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_137
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:05
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_790
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:05
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_571
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:05
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_137
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:06
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_790
    }
    vlans-enabled
    vs-index 7
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:20
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:20
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:21
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:21
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:21
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:22
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.tidwell.tx.hou.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:36
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:36
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:36
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:37
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:37
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:37
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:52
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:52
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:53
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:53
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:54
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:54
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 7
}

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:08
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_477
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:09
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_299
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:09
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_436
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:09
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_477
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:09
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_299
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:10
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_436
    }
    vlans-enabled
    vs-index 7
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:24
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:24
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:25
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:25
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:25
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:26
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:13:51:39
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:13:51:40
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:13:51:40
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:13:51:40
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:13:51:41
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:13:51:41
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 7
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:55
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_397
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:55
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_86
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:56
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_678
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:56
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_397
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:57
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_86
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:57
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_678
    }
    vlans-enabled
    vs-index 7
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:11
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_832
    }
    vlans-enabled
    vs-index 66
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:12
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_612
    }
    vlans-enabled
    vs-index 67
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:12
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_135
    }
    vlans-enabled
    vs-index 71
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:12
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_832
    }
    vlans-enabled
    vs-index 62
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:13
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_612
    }
    vlans-enabled
    vs-index 63
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:13
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_135
    }
    vlans-enabled
    vs-index 69
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:27
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_314
    }
    vlans-enabled
    vs-index 66
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:27
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_81
    }
    vlans-enabled
    vs-index 67
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:28
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_959
    }
    vlans-enabled
    vs-index 71
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:28
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_314
    }
    vlans-enabled
    vs-index 62
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:28
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_81
    }
    vlans-enabled
    vs-index 63
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:29
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_959
    }
    vlans-enabled
    vs-index 69
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:42
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_457
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:43
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_767
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:43
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_242
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:43
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_457
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:44
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_767
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:44
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_242
    }
    vlans-enabled
    vs-index 7
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:58
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 75
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:58
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 76
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:58
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 77
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:59
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 69
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:59
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 70
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:59
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 71
}

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.stonemtn.ga.bgsth.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2000 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_2001 { rules { ipv6_problemip_drop HTTPInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTP_v6_notag { rules { ipv6_problemip_drop HTTPInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2000 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2000 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_2001 { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_2001 } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm virtual RD0_HTTPS_v6_notag { rules { ipv6_problemip_drop HTTPSInspectBypass_v6_notag } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:53:13
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_553
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:53:14
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_377
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:53:14
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_309
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:53:15
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_553
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:53:15
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_377
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:53:15
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_309
    }
    vlans-enabled
    vs-index 7
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:18
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:18
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:19
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:19
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:20
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:20
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:35
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 111
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:36
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 112
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:36
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 113
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:37
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:37
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 106
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:37
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 107
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:52
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_995
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:53
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_871
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:53
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_426
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:53
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_995
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:54
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_871
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:46:54
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_426
    }
    vlans-enabled
    vs-index 7
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:08
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:08
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:09
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:09
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:09
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:10
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:24
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:24
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:24
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:25
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:25
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:26
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:40
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_887
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:40
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_625
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:40
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_479
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:40
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_887
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:41
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_625
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:41
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_479
    }
    vlans-enabled
    vs-index 7
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:55
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:55
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:55
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:56
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:56
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:47:56
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:13:48:10
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_636
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:13:48:10
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_410
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:13:48:10
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_282
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:13:48:11
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_636
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:13:48:11
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_410
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:13:48:11
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_282
    }
    vlans-enabled
    vs-index 7
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:25
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:26
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:26
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:26
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:27
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:27
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:41
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:42
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:42
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:42
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:43
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:43
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:57
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:58
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:58
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:59
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:59
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:48:59
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 7
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:14
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 111
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:14
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 112
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:15
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 113
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:15
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:15
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 106
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:16
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 107
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:31
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:31
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:32
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:32
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:32
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:33
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:48
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:48
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:48
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:49
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:49
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:49:49
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:04
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_571
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:04
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_137
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:05
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_790
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:05
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_571
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:05
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_137
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:06
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_790
    }
    vlans-enabled
    vs-index 7
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:20
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:20
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:21
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:21
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:21
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:22
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.tidwell.tx.hou.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:36
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:36
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:36
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:37
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:37
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:37
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:52
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:52
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:53
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:53
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:54
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:50:54
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 7
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:08
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_477
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:09
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_299
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:09
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_436
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:09
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_477
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:09
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_299
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:10
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_436
    }
    vlans-enabled
    vs-index 7
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:24
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 103
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:24
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 104
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:25
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 105
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:25
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 97
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:25
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 98
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:26
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 99
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:13:51:39
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:13:51:40
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:13:51:40
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:13:51:40
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:13:51:41
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:13:51:41
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 7
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:55
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_397
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:55
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_86
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:56
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_678
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:56
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_397
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:57
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_86
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:51:57
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_678
    }
    vlans-enabled
    vs-index 7
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:11
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_832
    }
    vlans-enabled
    vs-index 66
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:12
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_612
    }
    vlans-enabled
    vs-index 67
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:12
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_135
    }
    vlans-enabled
    vs-index 71
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:12
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_832
    }
    vlans-enabled
    vs-index 62
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:13
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_612
    }
    vlans-enabled
    vs-index 63
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:13
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_135
    }
    vlans-enabled
    vs-index 69
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:27
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_314
    }
    vlans-enabled
    vs-index 66
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:27
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_81
    }
    vlans-enabled
    vs-index 67
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:28
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_959
    }
    vlans-enabled
    vs-index 71
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:28
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_314
    }
    vlans-enabled
    vs-index 62
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:28
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_81
    }
    vlans-enabled
    vs-index 63
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:29
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_959
    }
    vlans-enabled
    vs-index 69
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:42
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_457
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:43
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_767
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:43
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_242
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:43
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_457
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:44
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_767
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:44
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_242
    }
    vlans-enabled
    vs-index 7
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:58
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 75
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:58
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 76
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:58
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 77
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:59
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_int
    }
    vlans-enabled
    vs-index 69
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:59
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_int
    }
    vlans-enabled
    vs-index 70
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:52:59
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_int
    }
    vlans-enabled
    vs-index 71
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.stonemtn.ga.bgsth.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2000 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:53:13
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_553
    }
    vlans-enabled
    vs-index 11
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_2001 {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:53:14
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_377
    }
    vlans-enabled
    vs-index 12
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTP_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTP_v6_notag {
    destination ::%0.http
    ip-protocol tcp
    last-modified-time 2022-11-29:18:53:14
    mask any6
    profiles {
        f5-wco-tcp { }
        http-transparent { }
    }
    rules {
        ipv6_problemip_drop
        HTTPInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_309
    }
    vlans-enabled
    vs-index 13
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2000
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2000 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:53:15
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2000
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2000_2_553
    }
    vlans-enabled
    vs-index 5
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_2001
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_2001 {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:53:15
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_2001
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2001_2_377
    }
    vlans-enabled
    vs-index 6
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm virtual RD0_HTTPS_v6_notag
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm virtual RD0_HTTPS_v6_notag {
    destination ::%0.https
    ip-protocol tcp
    last-modified-time 2022-11-29:18:53:15
    mask any6
    persist {
        ssl {
            default yes
        }
    }
    profiles {
        f5-wco-tcp { }
    }
    rules {
        ipv6_problemip_drop
        HTTPSInspectBypass_v6_notag
    }
    serverssl-use-sni disabled
    translate-address disabled
    translate-port disabled
    vlans {
        l2wire_vlan_2010_2_309
    }
    vlans-enabled
    vs-index 7
}
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
