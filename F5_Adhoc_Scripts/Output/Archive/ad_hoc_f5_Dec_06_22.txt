
######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.132nd-evertt.wa.sea.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.173-beaverto.or.port.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.300crls-pitt.pa.trr.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.8031-baltimo.md.belt.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

###############################################################################################
-----------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.area4.il.chi.comcast.net                   |
-----------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.brdgw-brkhvn.pa.free.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.broad-phldlp.pa.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        *************/32 { }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.bshps-mntlrl.nj.corp.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
********:3: The requested class IP item (/Common/problem_ipv6_dg 2001:1890:1286:221::4:1010 ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.chattanooga.tn.mdsth.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.fairvie-rose.mn.min.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.iowa-aurora.co.dnv.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#######################################################################################################
-------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.mntbl-albqrq.nm.nmex.comcast.net                   |
-------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pleasanton.ca.cal.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.pompano.fl.fla.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

################################################################################################
------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.sandy.ut.utah.comcast.net                   |
------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.taylor.mi.mich.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#################################################################################################
-------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.tidwell.tx.hou.comcast.net                   |
-------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

###################################################################################################
---------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.vinings.ga.bgsth.comcast.net                   |
---------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#########################################################################################################
---------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.washer-lowll.ma.neweng.comcast.net                   |
---------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ashburn.va.ndcasbn.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.ctc-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
********:3: The requested class IP item (/Common/problem_ipv6_dg 2001:1890:1286:221::4:1010 ffff:ffff:ffff:ffff:ffff:ffff:ffff:ffff) already exists in partition Common.
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

#####################################################################################################
-----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.jfk-phldlp.pa.corp.comcast.net                   |
-----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

######################################################################################################
------------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco02.boot-wstchs.pa.corp.comcast.net                   |
------------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}

####################################################################################################
----------------------------------------------------------------------------------------------------
|                  connecting to device - ccswco01.stonemtn.ga.bgsth.comcast.net                   |
----------------------------------------------------------------------------------------------------
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh modify ltm data-group internal problem_ipv6_dg records add { 2001:1890:1286:221::4:1010/128 { data clec.att.com } }
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh save sys config
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
Saving running configuration...
  /config/bigip.conf
  /config/bigip_base.conf
  /config/bigip_user.conf
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
tmsh list ltm data-group internal problem_ipv6_dg
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
ltm data-group internal problem_ipv6_dg {
    records {
        2a01:4f8:10a:3edc::2/128 {
            data lua.org
        }
        2a01:4f9:4a:23ec::1/128 {
            data nightlies.apache.org
        }
        2001:1890:1286:221::4:1010/128 {
            data clec.att.com
        }
        2002:cc5:2022::cc5:2022/128 {
            data fcbanking.com
        }
        2606:4700:3033::ac43:dd39/128 {
            data dropbox.barrowclift.me
        }
        2606:4700:3036::6815:18f5/128 {
            data dropbox.barrowclift.me
        }
        2620:49:8080:10::928e:418/128 {
            data data.bls.gov
        }
    }
    type ip
}
