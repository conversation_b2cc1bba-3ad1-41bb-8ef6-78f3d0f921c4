import requests
import urllib3
import pandas as pd
import os
import time
import difflib
import shutil


urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

basedir = os.path.dirname(os.path.abspath(__file__))
inp_version_file_worldwide = basedir + '\\current_version_worldwide.txt'
inp_version_file_usgovdod = basedir + '\\current_version_usgovdod.txt'
inp_version_file_usgovgcchigh = basedir + '\\current_version_usgovgcchigh.txt'
day = time.strftime('%b_%d_%y')

with open(inp_version_file_worldwide, 'r') as f:
    current_version_worldwide = f.read()
with open(inp_version_file_usgovdod, 'r') as f:
    current_version_usgovdod = f.read()
with open(inp_version_file_usgovgcchigh, 'r') as f:
    current_version_usgovgcchigh = f.read()

my_guid = '0468ef4a-ac38-4db8-8b3b-df84cff3caf1'


#check version
def ms_check_version(current_version_worldwide,inp_version_file_worldwide,current_version_usgovdod,inp_version_file_usgovdod,current_version_usgovgcchigh,inp_version_file_usgovgcchigh):
    version_url_worldwide = 'https://endpoints.office.com/version/Worldwide?clientrequestid=' + my_guid
    version_url_usgovdod = 'https://endpoints.office.com/version/USGovDoD?clientrequestid=' + my_guid
    version_url_usgovgcchigh = 'https://endpoints.office.com/version/USGovGCCHigh?clientrequestid=' + my_guid
    version_req_worldwide = requests.get(version_url_worldwide)
    version_req_usgovdod = requests.get(version_url_usgovdod)
    version_req_usgovgcchigh = requests.get(version_url_usgovgcchigh)
    if version_req_worldwide.status_code == 200:
        version_req_worldwide_json = version_req_worldwide.json()
        latest_version_worldwide = version_req_worldwide_json['latest']
        if latest_version_worldwide > current_version_worldwide:
            print('\nLooks like MS has updated "WORLDWIDE" entries !!!\n')
            with open(inp_version_file_worldwide, 'w') as f:
                f.write(latest_version_worldwide)
                f.close()
        else:
            print('\nVersions match for "WORLDWIDE". Nothing to worry!\n')
    else:
        print('Couldn\'t connect to MS WebService. Please check')
    if version_req_usgovdod.status_code == 200:
        version_req_usgovdod_json = version_req_usgovdod.json()
        latest_version_usgovdod = version_req_usgovdod_json['latest']
        if latest_version_usgovdod > current_version_usgovdod:
            print('\nLooks like MS has updated "US GOV DOD" entries !!!\n')
            with open(inp_version_file_usgovdod, 'w') as f:
                f.write(latest_version_usgovdod)
                f.close()
        else:
            print('\nVersions match for "US GOV DOD". Nothing to worry!\n')
    else:
        print('Couldn\'t connect to MS WebService. Please check')
    if version_req_usgovgcchigh.status_code == 200:
        version_req_usgovgcchigh_json = version_req_usgovgcchigh.json()
        latest_version_usgovgcchigh = version_req_usgovgcchigh_json['latest']
        if latest_version_usgovgcchigh > current_version_usgovgcchigh:
            print('\nLooks like MS has updated "US GOV GCC High" entries !!!\n')
            with open(inp_version_file_usgovgcchigh, 'w') as f:
                f.write(latest_version_usgovgcchigh)
                f.close()
        else:
            print('\nVersions match for "US GOV GCC High". Nothing to worry!\n')
    else:
        print('Couldn\'t connect to MS WebService. Please check')


if __name__ == "__main__":
    ms_check_version(current_version_worldwide,inp_version_file_worldwide,current_version_usgovdod,inp_version_file_usgovdod,current_version_usgovgcchigh,inp_version_file_usgovgcchigh)
    exit()