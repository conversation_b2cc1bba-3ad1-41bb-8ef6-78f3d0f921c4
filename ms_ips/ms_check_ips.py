import requests # type: ignore
import urllib3 # type: ignore
import pandas as pd # type: ignore
import os
import time
import difflib
import shutil


urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

basedir = os.path.dirname(os.path.abspath(__file__))
outdir = basedir + '\\output'
inp_version_file = basedir + '\\current_version.txt'
current_urls_file = basedir + '\\urls.txt'
current_ips_file = basedir + '\\ips.txt'
day = time.strftime('%b_%d_%y')
ips_out_file = outdir + '\\ips_out_file_' + day + '.txt'
urls_out_file = outdir + '\\urls_out_file_' + day + '.txt'

with open(inp_version_file, 'r') as f:
    current_version = f.read()
# current_version = int(current_version)

if os.path.exists(ips_out_file):
    os.remove(ips_out_file)

if os.path.exists(urls_out_file):
    os.remove(urls_out_file)

my_guid = '0468ef4a-ac38-4db8-8b3b-df84cff3caf1'


#check version
def ms_check_version(current_version, inp_version_file):
    # version_url = 'https://endpoints.office.com/version/USGovGCCHigh?clientrequestid=' + my_guid
    # version_url = 'https://endpoints.office.com/version/USGovDoD?clientrequestid=' + my_guid
    # version_url = 'https://endpoints.office.com/version/Worldwide?clientrequestid=' + my_guid
    version_url = 'https://endpoints.office.com/version/worldwide?clientrequestid=' + my_guid
    version_req = requests.get(version_url)
    if version_req.status_code == 200:
        version_req_json = version_req.json()
        latest_version = version_req_json['latest']
        # print(latest_version)
        # latest_version = int(latest_version)
        if latest_version > current_version:
            print('\nLooks like MS has updated entries, looking for changes, please standby ...\n')
            with open(inp_version_file, 'w') as f:
                f.write(latest_version)
                f.close()
            return 1
        else:
            print('\nVersions match. Nothing to worry!\n')
            return 0
    else:
        print('Couldn\'t connect to MS WebService. Please check')
        return 0

#get data from MS WebService
def ms_get_data(ips_out_file, urls_out_file):
    # data_url = 'https://endpoints.office.com/endpoints/USGovGCCHigh?clientrequestid=' + my_guid
    # data_url = 'https://endpoints.office.com/endpoints/USGovDoD?clientrequestid=' + my_guid
    # data_url = 'https://endpoints.office.com/endpoints/Worldwide?clientrequestid=' + my_guid
    data_url = 'https://endpoints.office.com/endpoints/worldwide?clientrequestid=' + my_guid
    data_req = requests.get(data_url)
    if data_req.status_code == 200:
        print('\nData obtained from MS WebService. Processing now ... \n')
        data_req_json = data_req.json()
        data_df = pd.DataFrame(data_req_json)
        data_df = data_df.fillna(0)
        ips_fin = []
        urls_fin = []
        for i in range(len(data_df)):
            if data_df.loc[i]['ips'] != 0:
                for z in data_df.loc[i]['ips']:
                    ips_fin.append(z)
        for i in range(len(data_df)):
            if data_df.loc[i]['urls'] != 0:
                for z in data_df.loc[i]['urls']:
                    urls_fin.append(z)
        ips_fin = sorted(set(ips_fin))
        urls_fin = sorted(set(urls_fin))
        with open(ips_out_file, 'w') as f:
            for i in ips_fin:
                f.write(f'{i}\n')
        with open(urls_out_file, 'w') as f:
            for i in urls_fin:
                f.write(f'{i}\n')
        print("\nSuccessfully written the new data to outdir ...\n")
    
#compare old vs new files and overwrite old files
def ms_compare_data(ips_out_file, urls_out_file, current_ips_file, current_urls_file):
    print("\nComparing new vs old data...\n")
    with open(current_ips_file, 'r') as f1, open(ips_out_file, 'r') as f2:
        ip_diff = difflib.unified_diff(f1.readlines(), f2.readlines(), fromfile=current_ips_file, tofile=ips_out_file)
    with open(current_urls_file, 'r') as f1, open(urls_out_file, 'r') as f2:
        url_diff = difflib.unified_diff(f1.readlines(), f2.readlines(), fromfile=current_urls_file, tofile=urls_out_file)
    print("\n")
    print("\nIP Comparison now...\n")
    for line in ip_diff:
        print(line)
    print("\nURL Comparison now...\n")
    for line in url_diff:
        print(line)
    shutil.copyfile(ips_out_file, current_ips_file)
    shutil.copyfile(urls_out_file, current_urls_file)

if __name__ == "__main__":
    version_check = ms_check_version(current_version, inp_version_file)
    if version_check!=0:
        ms_get_data(ips_out_file, urls_out_file)
        ms_compare_data(ips_out_file, urls_out_file, current_ips_file, current_urls_file)