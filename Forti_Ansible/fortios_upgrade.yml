---
- hosts: first
  vars:
   host: "************"
   username: "admin"
   password: "siec0mcast!"
   vdom: "root"
   ssl_verify: "False"
  tasks:
  - name: Perform firmware upgrade with local firmware file.
    fortios_system_firmware:
      host:  "{{ host }}"
      username: "{{ username }}"
      password: "{{ password }}"
      vdom:  "{{ vdom }}"
      ssl_verify: "False"
      system_firmware:
        file_content: "<your_own_value>"
        filename: "<your_own_value>"
        format_partition: "<your_own_value>"
        source: "upload"
    register: fortios_system_firmware_upgrade_result

  - debug:
      var:
        # please check the following status to confirm
        fortios_system_firmware_upgrade_result.meta.results.status
...