import re
import datetime
import time
import logging
from ipaddress import ip_network as ipn
from ipaddress import ip_address as ipa
from typing import List, <PERSON><PERSON>
from pyspark.sql import SparkSession, DataFrame
from pyspark.sql import functions as F
from pyspark.sql.functions import udf, broadcast
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, TimestampType, BooleanType

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class Config:
    """Configuration class for the NDC Proxy pipeline"""

    # Date configuration
    DAYS_TO_PROCESS = 2
    DATE_FORMAT = "%m-%d-%Y"

    # Table names
    FIREWALL_TABLE = "ccs_titan_de.alcyonia.f5_firewall_flatten"
    ASSET_TABLE = "ccs_titan_de.alcyonia.solid_asset_source_curr"
    IP_CONTROL_TABLE = "ccs_titan_de.alcyonia.ipcontrol_curr"
    OUTPUT_TABLE = "ccs_titan_partners.ccs_nse.ndc_blue_fw_ia_logs_30days"

    # Filter criteria
    ACL_RULE_FILTER = "%Internet_Access_Rule"

    # Performance settings
    COALESCE_PARTITIONS = 1  # Consider increasing for larger datasets

    # Sorting configuration
    SORT_COLUMNS = [
        ("src_ip", "asc"),           # Primary sort: source IP ascending
        ("fw_timestamp", "desc")        # Secondary sort: timestamp descending (most recent first)
    ]



def get_date_ranges(days: int = Config.DAYS_TO_PROCESS) -> List[Tuple[int, int, int]]:
    """
    Generate date ranges for the last N days

    Args:
        days: Number of days to go back

    Returns:
        List of tuples containing (day, month, year)
    """
    dates = []
    for x in range(days, 0, -1):
        temp_date = datetime.datetime.now() - datetime.timedelta(days=x)
        day = int(temp_date.strftime("%d"))
        month = int(temp_date.strftime("%m"))
        year = int(temp_date.strftime("%Y"))
        dates.append((day, month, year))

    logger.info(f"Generated date ranges: {dates}")
    return dates

def get_formatted_date() -> str:
    """Get today's date in the configured format"""
    return datetime.datetime.now().strftime(Config.DATE_FORMAT)


def load_firewall_data(spark, date_ranges: List[Tuple[int, int, int]]) -> DataFrame:
    """
    Load and union firewall data for multiple date ranges

    Args:
        spark: Spark session
        date_ranges: List of (day, month, year) tuples

    Returns:
        Combined DataFrame with firewall data
    """
    logger.info(f"Loading firewall data for {len(date_ranges)} date ranges")

    # Build filter conditions for all dates at once (more efficient)
    date_conditions = []
    for day, month, year in date_ranges:
        date_condition = (
            (F.col('y') == year) &
            (F.col('m') == month) &
            (F.col('d') == day)
        )
        date_conditions.append(date_condition)

    # Combine all date conditions with OR
    combined_date_filter = date_conditions[0]
    for condition in date_conditions[1:]:
        combined_date_filter = combined_date_filter | condition
    # Testing sample data
    combined_date_filter = ((F.col('y') == 2025) & (F.col('m') == 6) & (F.col('d') == 12) & (F.col('h') == 8))


    # Load data with combined filter (single table scan instead of multiple)
    flow_table = (spark.table(Config.FIREWALL_TABLE)
                  .filter(F.col('acl_rule_name').like(Config.ACL_RULE_FILTER))
                  .filter(combined_date_filter)
                  .cache())  # Cache for reuse
    new_flow_table = flow_table.select("src_ip")
    deduped_new_flow_table = new_flow_table.dropDuplicates(['src_ip'])
    logger.info(f"Loaded firewall data with {flow_table.count()} records")
    deduped_new_flow_table.show()
    return deduped_new_flow_table

def load_asset_data(spark) -> DataFrame:
    """Load asset/inventory data"""
    logger.info("Loading asset data")
    asset_data = spark.table(Config.ASSET_TABLE)
    logger.info(f"Loaded asset data with {asset_data.count()} records")
    return asset_data

def convert_to_ipn(block):
    return str(ipn(block))

def convert_to_ipa(address):
    return str(ipa(address))

def is_in_ip_network(ip, subnet):
    try:
        if ipa(ip) in ipn(subnet, strict=False):
            return subnet
        else:
            return 0
    except ValueError:
        return 0
is_in_ip_network_udf = udf(is_in_ip_network, StringType())

def load_ip_control_data(spark) -> DataFrame:
    """Load IP control data"""
    logger.info("Loading IP control data")
    ip_control_data = spark.table(Config.IP_CONTROL_TABLE).select("Block")
    logger.info(f"Loaded IP control data with {ip_control_data.count()} records")
    logger.info(f"converting IP control data into subnet {ip_control_data.count()} records")
    convert_to_ipn_udf = udf(convert_to_ipn, StringType())
    ip_control_converted_df = ip_control_data.withColumn("Block", convert_to_ipn_udf(ip_control_data["Block"]))
    broadcasted_ip_control_df = broadcast(ip_control_converted_df)
    broadcasted_ip_control_df.show()
    return broadcasted_ip_control_df

# def deduplicate_by_ip(df: DataFrame) -> DataFrame:
#     """Remove duplicate records based on source IP"""
#     logger.info("Deduplicating records by source IP")
#     deduped_df = df.dropDuplicates(['src_ip'])
#     logger.info(f"After deduplication: {deduped_df.count()} records")
#     return deduped_df

def join_flow_and_asset_data(flow_df: DataFrame, asset_df: DataFrame) -> DataFrame:
    """
    Join flow data with asset data and handle null values

    Args:
        flow_df: Deduplicated flow data
        asset_df: Asset inventory data

    Returns:
        Joined DataFrame with cleaned app_group column
    """
    logger.info("Joining flow and asset data")

    # Perform left join
    joined_df = flow_df.join(
        asset_df,
        flow_df.src_ip == asset_df.ip_v4_address,
        "left"
    )

    # Handle null values in app_group (fix typo: "UNKOWN" -> "UNKNOWN")
    final_df = joined_df.withColumn(
        'app_group',
        F.when(F.col('app_group').isNull(), "UNKNOWN")
         .otherwise(F.col('app_group'))
    )

    logger.info(f"Join completed with {final_df.count()} records")
    return final_df

def ipcontrol_checks(fw_df: DataFrame) -> DataFrame:
    """
    Perform IP control checks and add new columns

    Args:
        TBD

    Returns:
        DataFrame with additional columns for IP control checks
    """
    logger.info("Performing IP control checks")

    # Broadcast IP control data for efficient join
    df = spark.table("ccs_titan_de.alcyonia.ipcontrol_curr").select("Block")
    df_converted = df.withColumn("Block", convert_to_ipn_udf(df["Block"]))

def save_to_outputs(df: DataFrame) -> None:

    logger.info("Saving sorted data to outputs")

    # Build sort expressions from configuration
    sort_expressions = []
    sort_descriptions = []

    for column_name, sort_order in Config.SORT_COLUMNS:
        if sort_order.lower() == "desc":
            sort_expressions.append(F.col(column_name).desc_nulls_last())
            sort_descriptions.append(f"{column_name} (descending)")
        else:
            sort_expressions.append(F.col(column_name).asc_nulls_last())
            sort_descriptions.append(f"{column_name} (ascending)")

    # Sort the data before saving
    sorted_df = df.orderBy(*sort_expressions)

    logger.info(f"Data sorted by: {', '.join(sort_descriptions)}")

    # Save to Delta table with sorted data
    sorted_df.coalesce(Config.COALESCE_PARTITIONS).write \
      .format("delta") \
      .option("mergeSchema", "true") \
      .mode("overwrite") \
      .saveAsTable(Config.OUTPUT_TABLE)

    logger.info(f"Saved {sorted_df.count()} sorted records to both CSV and Delta table")


def main():
    """Main pipeline execution function"""
    try:
        logger.info("Starting NDC Proxy data processing pipeline")

        # Get current date and date ranges
        formatted_date = get_formatted_date()
        date_ranges = get_date_ranges()

        # Load data
        flow_data = load_firewall_data(spark, date_ranges)
        asset_data = load_asset_data(spark)

        # Process data
        # deduped_flow_data = deduplicate_by_ip(flow_data)
        final_data = join_flow_and_asset_data(deduped_flow_data, asset_data)

        # Save outputs
        save_to_outputs(final_data)


        # Cleanup cached data
        flow_data.unpersist()

        logger.info("Pipeline completed successfully")

    except Exception as e:
        logger.error(f"Pipeline failed with error: {e}")
        raise

# Execute the pipeline
if __name__ == "__main__":
    main()
