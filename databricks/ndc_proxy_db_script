# Databricks notebook source
# MAGIC %md
# MAGIC # NDC Proxy Data Processing Pipeline
# MAGIC
# MAGIC ## Workflow:
# MAGIC 1. Read in inventory data
# MAGIC 2. Read in scan results for the last 7 days
# MAGIC 3. De-duplicate scan results based on IP address
# MAGIC 4. Join inventory and de-duped scan results
# MAGIC 5. Report on null values for last scanned in join
# MAGIC 6. Export results to S3 and Delta table
# MAGIC 7. Clean up old backup files

# COMMAND ----------

# MAGIC %md
# MAGIC ## Configuration and Imports

# COMMAND ----------

import re
import datetime
import time
import logging
from typing import List, Tuple
from pyspark.sql import SparkSession, DataFrame
from pyspark.sql import functions as F
from pyspark.sql.types import StructType, StructField, StringType, IntegerType, TimestampType, BooleanType

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Configuration Parameters

# COMMAND ----------

class Config:
    """Configuration class for the NDC Proxy pipeline"""

    # Date configuration
    DAYS_TO_PROCESS = 7
    DATE_FORMAT = "%m-%d-%Y"
    BACKUP_RETENTION_DAYS = 7

    # Table names
    FIREWALL_TABLE = "ccs_titan_de.alcyonia.f5_firewall_flatten"
    ASSET_TABLE = "ccs_titan_de.alcyonia.solid_asset_source_curr"
    OUTPUT_TABLE = "ccs_titan_partners.ccs_nse.ndc_proxy_7d_logs"

    # S3 paths
    S3_OUTPUT_PATH = "s3://tpx-sse-nse/data/ndc-proxy7day"
    S3_BACKUP_PATH = "s3://tpx-sse-nse/data/ndc-proxy7day/backup"

    # Filter criteria
    ACL_RULE_FILTER = "%Internet_Access_Rule"

    # Performance settings
    COALESCE_PARTITIONS = 1  # Consider increasing for larger datasets

    # Sorting configuration
    SORT_COLUMNS = [
        ("src_ip", "asc"),           # Primary sort: source IP ascending
        ("timestamp", "desc")        # Secondary sort: timestamp descending (most recent first)
    ]

# COMMAND ----------

# MAGIC %md
# MAGIC ## Utility Functions

# COMMAND ----------

def get_date_ranges(days: int = Config.DAYS_TO_PROCESS) -> List[Tuple[int, int, int]]:
    """
    Generate date ranges for the last N days

    Args:
        days: Number of days to go back

    Returns:
        List of tuples containing (day, month, year)
    """
    dates = []
    for x in range(days, 0, -1):
        temp_date = datetime.datetime.now() - datetime.timedelta(days=x)
        day = int(temp_date.strftime("%d"))
        month = int(temp_date.strftime("%m"))
        year = int(temp_date.strftime("%Y"))
        dates.append((day, month, year))

    logger.info(f"Generated date ranges: {dates}")
    return dates

def get_formatted_date() -> str:
    """Get today's date in the configured format"""
    return datetime.datetime.now().strftime(Config.DATE_FORMAT)


# COMMAND ----------

# MAGIC %md
# MAGIC ## Data Processing Functions

# COMMAND ----------

def load_firewall_data(spark, date_ranges: List[Tuple[int, int, int]]) -> DataFrame:
    """
    Load and union firewall data for multiple date ranges

    Args:
        spark: Spark session
        date_ranges: List of (day, month, year) tuples

    Returns:
        Combined DataFrame with firewall data
    """
    logger.info(f"Loading firewall data for {len(date_ranges)} date ranges")

    # Build filter conditions for all dates at once (more efficient)
    date_conditions = []
    for day, month, year in date_ranges:
        date_condition = (
            (F.col('y') == year) &
            (F.col('m') == month) &
            (F.col('d') == day)
        )
        date_conditions.append(date_condition)

    # Combine all date conditions with OR
    combined_date_filter = date_conditions[0]
    for condition in date_conditions[1:]:
        combined_date_filter = combined_date_filter | condition

    # Load data with combined filter (single table scan instead of multiple)
    flow_table = (spark.table(Config.FIREWALL_TABLE)
                  .filter(F.col('acl_rule_name').like(Config.ACL_RULE_FILTER))
                  .filter(combined_date_filter)
                  .cache())  # Cache for reuse

    logger.info(f"Loaded firewall data with {flow_table.count()} records")
    return flow_table

def load_asset_data(spark) -> DataFrame:
    """Load asset/inventory data"""
    logger.info("Loading asset data")
    asset_data = spark.table(Config.ASSET_TABLE)
    logger.info(f"Loaded asset data with {asset_data.count()} records")
    return asset_data

def deduplicate_by_ip(df: DataFrame) -> DataFrame:
    """Remove duplicate records based on source IP"""
    logger.info("Deduplicating records by source IP")
    deduped_df = df.dropDuplicates(['src_ip'])
    logger.info(f"After deduplication: {deduped_df.count()} records")
    return deduped_df

def join_flow_and_asset_data(flow_df: DataFrame, asset_df: DataFrame) -> DataFrame:
    """
    Join flow data with asset data and handle null values

    Args:
        flow_df: Deduplicated flow data
        asset_df: Asset inventory data

    Returns:
        Joined DataFrame with cleaned app_group column
    """
    logger.info("Joining flow and asset data")

    # Perform left join
    joined_df = flow_df.join(
        asset_df,
        flow_df.src_ip == asset_df.ip_v4_address,
        "left"
    )

    # Handle null values in app_group (fix typo: "UNKOWN" -> "UNKNOWN")
    final_df = joined_df.withColumn(
        'app_group',
        F.when(F.col('app_group').isNull(), "UNKNOWN")
         .otherwise(F.col('app_group'))
    )

    logger.info(f"Join completed with {final_df.count()} records")
    return final_df

# COMMAND ----------

# MAGIC %md
# MAGIC ## File Management Functions

# COMMAND ----------

def save_to_outputs(df: DataFrame, formatted_date: str) -> str:
    """
    Save DataFrame to both CSV and Delta table with sorted output

    Args:
        df: DataFrame to save
        formatted_date: Date string for file naming

    Returns:
        Path to the saved CSV file
    """
    logger.info("Saving sorted data to outputs")

    # Build sort expressions from configuration
    sort_expressions = []
    sort_descriptions = []

    for column_name, sort_order in Config.SORT_COLUMNS:
        if sort_order.lower() == "desc":
            sort_expressions.append(F.col(column_name).desc().nulls_last())
            sort_descriptions.append(f"{column_name} (descending)")
        else:
            sort_expressions.append(F.col(column_name).asc().nulls_last())
            sort_descriptions.append(f"{column_name} (ascending)")

    # Sort the data before saving
    sorted_df = df.orderBy(*sort_expressions)

    logger.info(f"Data sorted by: {', '.join(sort_descriptions)}")

    # Save to CSV (consider increasing partitions for larger datasets)
    sorted_df.coalesce(Config.COALESCE_PARTITIONS).write \
      .option("header", "true") \
      .mode("overwrite") \
      .csv(Config.S3_OUTPUT_PATH)

    # Save to Delta table with sorted data
    sorted_df.coalesce(Config.COALESCE_PARTITIONS).write \
      .format("delta") \
      .option("mergeSchema", "true") \
      .mode("overwrite") \
      .saveAsTable(Config.OUTPUT_TABLE)

    logger.info(f"Saved {sorted_df.count()} sorted records to both CSV and Delta table")

    # Rename CSV file to predictable name
    return rename_csv_file(formatted_date)

def rename_csv_file(formatted_date: str) -> str:
    """
    Rename the generated CSV file to a predictable name

    Args:
        formatted_date: Date string for file naming

    Returns:
        New file path
    """
    try:
        temp_list = dbutils.fs.ls(Config.S3_OUTPUT_PATH)
        csv_files = [f.name for f in temp_list if f.name.endswith('.csv')]

        if not csv_files:
            raise ValueError("No CSV files found after write operation")

        old_name = f"{Config.S3_OUTPUT_PATH}/{csv_files[0]}"
        new_name = f"{Config.S3_OUTPUT_PATH}/ndc-proxy7d-{formatted_date}.csv"

        dbutils.fs.mv(old_name, new_name)
        logger.info(f"Renamed file from {old_name} to {new_name}")

        return new_name
    except Exception as e:
        logger.error(f"Error renaming CSV file: {e}")
        raise

def backup_file(file_path: str, formatted_date: str) -> None:
    """
    Create backup copy of the file

    Args:
        file_path: Source file path
        formatted_date: Date string for backup naming
    """
    try:
        backup_name = f"ndc-proxy7d-{formatted_date}.csv"
        backup_path = f"{Config.S3_BACKUP_PATH}/{backup_name}"

        dbutils.fs.cp(file_path, backup_path)
        logger.info(f"Created backup at {backup_path}")
    except Exception as e:
        logger.error(f"Error creating backup: {e}")
        raise

def cleanup_old_backups() -> None:
    """Remove backup files older than retention period"""
    try:
        cutoff_time = time.time() * 1000 - (Config.BACKUP_RETENTION_DAYS * 24 * 60 * 60 * 1000)
        file_list = dbutils.fs.ls(Config.S3_BACKUP_PATH)

        deleted_count = 0
        for file in file_list:
            if file.modificationTime < cutoff_time:
                dbutils.fs.rm(file.path)
                deleted_count += 1

        logger.info(f"Cleaned up {deleted_count} old backup files")
    except Exception as e:
        logger.error(f"Error during backup cleanup: {e}")
        # Don't raise - cleanup failure shouldn't stop the pipeline

# COMMAND ----------

# MAGIC %md
# MAGIC ## Main Pipeline Execution

# COMMAND ----------

def main():
    """Main pipeline execution function"""
    try:
        logger.info("Starting NDC Proxy data processing pipeline")

        # Get current date and date ranges
        formatted_date = get_formatted_date()
        date_ranges = get_date_ranges()

        # Load data
        flow_data = load_firewall_data(spark, date_ranges)
        asset_data = load_asset_data(spark)

        # Process data
        deduped_flow_data = deduplicate_by_ip(flow_data)
        final_data = join_flow_and_asset_data(deduped_flow_data, asset_data)

        # Save outputs
        csv_file_path = save_to_outputs(final_data, formatted_date)

        # Backup and cleanup
        backup_file(csv_file_path, formatted_date)
        cleanup_old_backups()

        # Cleanup cached data
        flow_data.unpersist()

        logger.info("Pipeline completed successfully")

    except Exception as e:
        logger.error(f"Pipeline failed with error: {e}")
        raise

# Execute the pipeline
if __name__ == "__main__":
    main()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Pipeline Complete
# MAGIC
# MAGIC The NDC Proxy data processing pipeline has completed. Check the logs above for any issues or warnings.