# Databricks notebook source
# MAGIC %md
# MAGIC # Delta Table Export Pipeline
# MAGIC
# MAGIC ## Purpose:
# MAGIC Export NDC Proxy 7-day logs from Delta table to CSV format for external consumption
# MAGIC
# MAGIC ## Features:
# MAGIC - Configurable output paths and formats
# MAGIC - Data validation and quality checks
# MAGIC - Performance optimization based on data size
# MAGIC - Comprehensive error handling and logging
# MAGIC - Automatic file naming with timestamps

# COMMAND ----------

# MAGIC %md
# MAGIC ## Configuration and Imports

# COMMAND ----------

import datetime
import logging
from typing import Optional, Dict, Any
from pyspark.sql import DataFrame
from pyspark.sql import functions as F
from pyspark.sql.types import StringType

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Configuration Class

# COMMAND ----------

class ExportConfig:
    """Configuration for Delta table export pipeline"""

    # Source table
    SOURCE_TABLE = "ccs_titan_partners.ccs_nse.ndc_proxy_7d_logs"

    # Output configuration
    OUTPUT_BASE_PATH = "dbfs:/Volumes/ccs_titan_partners/ccs_nse/ccs_nse_files"
    OUTPUT_FILE_PREFIX = "ndc_proxy_logs"
    DATE_FORMAT = "%m%d%y"  # Format: 060425

    # Export formats
    EXPORT_FORMATS = {
        "csv": {
            "format": "csv",
            "options": {
                "header": "true",
                "delimiter": ",",
                "quote": '"',
                "escape": '"'
            }
        },
        "tsv": {
            "format": "csv",
            "options": {
                "header": "true",
                "delimiter": "\t",
                "quote": '"',
                "escape": '"'
            }
        }
    }

    # Performance settings
    SMALL_DATA_THRESHOLD = 1000000  # 1M records
    MEDIUM_DATA_THRESHOLD = 10000000  # 10M records

    # Partitioning strategy based on data size
    PARTITION_STRATEGY = {
        "small": 1,      # < 1M records: single file
        "medium": 4,     # 1M-10M records: 4 partitions
        "large": 16      # > 10M records: 16 partitions
    }

    # Data validation
    REQUIRED_COLUMNS = ["src_ip", "timestamp"]  # Minimum required columns
    MAX_NULL_PERCENTAGE = 50  # Maximum allowed null percentage for key columns

# COMMAND ----------

# MAGIC %md
# MAGIC ## Data Validation Functions

# COMMAND ----------

def validate_data_quality(df: DataFrame) -> Dict[str, Any]:
    """
    Validate data quality and return metrics

    Args:
        df: DataFrame to validate

    Returns:
        Dictionary with validation results
    """
    logger.info("Starting data quality validation")

    validation_results = {
        "total_records": 0,
        "column_count": 0,
        "null_percentages": {},
        "validation_passed": True,
        "issues": []
    }

    try:
        # Basic metrics
        total_records = df.count()
        column_count = len(df.columns)

        validation_results["total_records"] = total_records
        validation_results["column_count"] = column_count

        logger.info(f"Data contains {total_records:,} records with {column_count} columns")

        # Check for required columns
        missing_columns = set(ExportConfig.REQUIRED_COLUMNS) - set(df.columns)
        if missing_columns:
            validation_results["validation_passed"] = False
            validation_results["issues"].append(f"Missing required columns: {missing_columns}")

        # Calculate null percentages for key columns
        for col in ExportConfig.REQUIRED_COLUMNS:
            if col in df.columns:
                null_count = df.filter(F.col(col).isNull()).count()
                null_percentage = (null_count / total_records) * 100 if total_records > 0 else 0
                validation_results["null_percentages"][col] = null_percentage

                if null_percentage > ExportConfig.MAX_NULL_PERCENTAGE:
                    validation_results["validation_passed"] = False
                    validation_results["issues"].append(
                        f"Column '{col}' has {null_percentage:.1f}% null values (exceeds {ExportConfig.MAX_NULL_PERCENTAGE}% threshold)"
                    )

        # Check for empty dataset
        if total_records == 0:
            validation_results["validation_passed"] = False
            validation_results["issues"].append("Dataset is empty")

        logger.info(f"Data validation completed. Passed: {validation_results['validation_passed']}")

        return validation_results

    except Exception as e:
        logger.error(f"Error during data validation: {e}")
        validation_results["validation_passed"] = False
        validation_results["issues"].append(f"Validation error: {str(e)}")
        return validation_results

# COMMAND ----------

# MAGIC %md
# MAGIC ## Export Functions

# COMMAND ----------

def determine_partition_strategy(record_count: int) -> str:
    """
    Determine optimal partitioning strategy based on data size

    Args:
        record_count: Number of records in the dataset

    Returns:
        Partition strategy key ('small', 'medium', 'large')
    """
    if record_count < ExportConfig.SMALL_DATA_THRESHOLD:
        return "small"
    elif record_count < ExportConfig.MEDIUM_DATA_THRESHOLD:
        return "medium"
    else:
        return "large"

def prepare_data_for_export(df: DataFrame) -> DataFrame:
    """
    Prepare DataFrame for export by handling complex data types

    Args:
        df: Source DataFrame

    Returns:
        DataFrame with all columns cast to string for CSV compatibility
    """
    logger.info("Preparing data for export")

    # Cast all columns to string to handle complex data types
    string_columns = []
    for col_name in df.columns:
        string_columns.append(F.col(col_name).cast(StringType()).alias(col_name))

    prepared_df = df.select(*string_columns)
    logger.info(f"Converted {len(df.columns)} columns to string type")

    return prepared_df

def generate_output_path(base_path: str, file_prefix: str, export_format: str,
                        date_str: Optional[str] = None) -> str:
    """
    Generate output path with timestamp

    Args:
        base_path: Base directory path
        file_prefix: File name prefix
        export_format: Export format (csv, tsv, etc.)
        date_str: Optional date string, defaults to current date

    Returns:
        Complete output path
    """
    if date_str is None:
        date_str = datetime.datetime.now().strftime(ExportConfig.DATE_FORMAT)

    filename = f"{file_prefix}_{date_str}"
    return f"{base_path}/{filename}"

def export_to_format(df: DataFrame, output_path: str, export_format: str,
                    partition_count: int) -> Dict[str, Any]:
    """
    Export DataFrame to specified format

    Args:
        df: DataFrame to export
        output_path: Output path
        export_format: Format to export to ('csv', 'tsv')
        partition_count: Number of partitions to use

    Returns:
        Export results dictionary
    """
    logger.info(f"Exporting to {export_format} format at {output_path}")

    try:
        format_config = ExportConfig.EXPORT_FORMATS[export_format]

        # Apply partitioning strategy
        if partition_count == 1:
            export_df = df.coalesce(1)
            logger.info("Using single partition (coalesce)")
        else:
            export_df = df.repartition(partition_count)
            logger.info(f"Using {partition_count} partitions")

        # Write data
        writer = export_df.write.mode("overwrite")

        # Apply format-specific options
        for option_key, option_value in format_config["options"].items():
            writer = writer.option(option_key, option_value)

        # Execute write
        writer.format(format_config["format"]).save(output_path)

        logger.info(f"Successfully exported data to {output_path}")

        return {
            "success": True,
            "output_path": output_path,
            "format": export_format,
            "partitions": partition_count
        }

    except Exception as e:
        logger.error(f"Error exporting to {export_format}: {e}")
        return {
            "success": False,
            "error": str(e),
            "output_path": output_path,
            "format": export_format
        }

# COMMAND ----------

# MAGIC %md
# MAGIC ## Main Export Pipeline

# COMMAND ----------

def export_delta_table(table_name: str = ExportConfig.SOURCE_TABLE,
                      export_formats: list = ["csv"],
                      custom_date: Optional[str] = None) -> Dict[str, Any]:
    """
    Main function to export Delta table to various formats

    Args:
        table_name: Name of the Delta table to export
        export_formats: List of formats to export to
        custom_date: Optional custom date string for file naming

    Returns:
        Dictionary with export results
    """
    pipeline_results = {
        "start_time": datetime.datetime.now(),
        "table_name": table_name,
        "exports": [],
        "validation": {},
        "success": False
    }

    try:
        logger.info(f"Starting export pipeline for table: {table_name}")

        # Load data from Delta table
        logger.info("Loading data from Delta table")
        df = spark.read.table(table_name)

        # Validate data quality
        validation_results = validate_data_quality(df)
        pipeline_results["validation"] = validation_results

        if not validation_results["validation_passed"]:
            logger.error("Data validation failed:")
            for issue in validation_results["issues"]:
                logger.error(f"  - {issue}")
            pipeline_results["success"] = False
            return pipeline_results

        # Determine optimal partitioning
        record_count = validation_results["total_records"]
        partition_strategy = determine_partition_strategy(record_count)
        partition_count = ExportConfig.PARTITION_STRATEGY[partition_strategy]

        logger.info(f"Using '{partition_strategy}' strategy with {partition_count} partitions for {record_count:,} records")

        # Prepare data for export
        prepared_df = prepare_data_for_export(df)

        # Export to each requested format
        for export_format in export_formats:
            if export_format not in ExportConfig.EXPORT_FORMATS:
                logger.warning(f"Unsupported export format: {export_format}")
                continue

            output_path = generate_output_path(
                ExportConfig.OUTPUT_BASE_PATH,
                ExportConfig.OUTPUT_FILE_PREFIX,
                export_format,
                custom_date
            )

            export_result = export_to_format(
                prepared_df,
                output_path,
                export_format,
                partition_count
            )

            pipeline_results["exports"].append(export_result)

        # Check if all exports succeeded
        successful_exports = [exp for exp in pipeline_results["exports"] if exp["success"]]
        pipeline_results["success"] = len(successful_exports) == len(export_formats)

        pipeline_results["end_time"] = datetime.datetime.now()
        pipeline_results["duration"] = pipeline_results["end_time"] - pipeline_results["start_time"]

        logger.info(f"Export pipeline completed. Success: {pipeline_results['success']}")
        logger.info(f"Duration: {pipeline_results['duration']}")

        return pipeline_results

    except Exception as e:
        logger.error(f"Export pipeline failed: {e}")
        pipeline_results["success"] = False
        pipeline_results["error"] = str(e)
        pipeline_results["end_time"] = datetime.datetime.now()
        return pipeline_results

# COMMAND ----------

# MAGIC %md
# MAGIC ## Execute Export Pipeline

# COMMAND ----------

# Execute the export pipeline
if __name__ == "__main__":
    # Configuration for this run
    EXPORT_FORMATS = ["csv"]  # Can be ["csv", "tsv"] for multiple formats
    CUSTOM_DATE = None  # Set to specific date string if needed, e.g., "060425"

    # Run the export
    results = export_delta_table(
        export_formats=EXPORT_FORMATS,
        custom_date=CUSTOM_DATE
    )

    # Display results
    print("\n" + "="*60)
    print("EXPORT PIPELINE RESULTS")
    print("="*60)
    print(f"Table: {results['table_name']}")
    print(f"Success: {results['success']}")
    print(f"Duration: {results.get('duration', 'N/A')}")

    if 'validation' in results:
        validation = results['validation']
        print(f"\nData Validation:")
        print(f"  Records: {validation['total_records']:,}")
        print(f"  Columns: {validation['column_count']}")
        print(f"  Validation Passed: {validation['validation_passed']}")

        if validation['null_percentages']:
            print(f"  Null Percentages:")
            for col, pct in validation['null_percentages'].items():
                print(f"    {col}: {pct:.1f}%")

    print(f"\nExports:")
    for export in results.get('exports', []):
        status = "✅ SUCCESS" if export['success'] else "❌ FAILED"
        print(f"  {export['format'].upper()}: {status}")
        if export['success']:
            print(f"    Path: {export['output_path']}")
            print(f"    Partitions: {export['partitions']}")
        else:
            print(f"    Error: {export.get('error', 'Unknown error')}")

    if not results['success']:
        if 'error' in results:
            print(f"\nPipeline Error: {results['error']}")
        if 'validation' in results and results['validation']['issues']:
            print(f"\nValidation Issues:")
            for issue in results['validation']['issues']:
                print(f"  - {issue}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Pipeline Complete
# MAGIC
# MAGIC The Delta table export pipeline has completed. Check the results above for export status and file locations.