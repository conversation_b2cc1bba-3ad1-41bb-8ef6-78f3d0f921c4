import requests
import sys
import base64
import os
import time
import pandas as pd
from multiprocessing import Process, Manager, Pool, cpu_count
import urllib3
import smtplib,ssl
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email.mime.text import MIMEText
from email.utils import formatdate
from email import encoders
from email.utils import COMMASPACE, formatdate

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

sec_path = b'L2FwcC9zaWVfcHJvamVjdHMvc2llX2lib3NzL2lucHV0'
secr_path = (base64.b64decode(sec_path)).decode('utf-8')
sys.path.insert(1, secr_path)
from iboss_get_secrets import *
iboss_admin = get_sec(iboss_admin)
iboss_pass = get_sec(iboss_pass)

basedir = os.path.dirname(os.path.abspath(__file__))
indir = basedir + '/input'
outdir = basedir + '/output'

day = time.strftime('%b_%d_%y')
iboss_details = indir + '/iboss_details.csv'
iboss_details_df = pd.read_csv(iboss_details)
iboss_details_list = iboss_details_df['name'].to_list()
iboss_out_file = outdir + '/iboss_netid_list_' + day + '.txt'
f_email_temp_s = indir + '/success_template.txt'
f_email_temp_f1 = indir + '/failure_template.txt'
f_email_temp_f2 = indir + '/failure_sign_template.txt'

if os.path.exists(iboss_out_file):
    os.remove(iboss_out_file)

with open(f_email_temp_s, 'r') as f:
    email_temp_s = f.read()
with open(f_email_temp_f1, 'r') as f:
    email_temp_f1 = f.read()
with open(f_email_temp_f2, 'r') as f:
    email_temp_f2 = f.read()

def sendMail(sfrom, to, subject, text, files=[]):
    msg = MIMEMultipart()
    msg['To'] = COMMASPACE.join(to)
    msg['Date'] = formatdate(localtime=True)
    msg['Subject'] = subject
    
    msg.attach( MIMEText(text) )
    
    for file in files:
        part = MIMEBase('application', "octet-stream")
        part.set_payload( open(file,"rb").read() )
        encoders.encode_base64(part)
        part.add_header('Content-Disposition', 'attachment; filename="%s"'
                       % os.path.basename(file))
        msg.attach(part)
    
    server = smtplib.SMTP('mailrelay.comcast.com')
    server.sendmail(sfrom, to, msg.as_string())
    print ('Done', flush=True)
    server.quit()

def delete_iboss_netid(ibnd,iboss_node):
    proxies = {'https': 'ccspcg-ash-01p.wco.comcast.net:8083'}
    data = {"userName":iboss_admin, "x":iboss_pass, "ldapServer":""}
    login_url = 'https://' + iboss_node + '/json/login'
    iboss_login = requests.post(login_url, json=data, proxies=proxies, verify=False)
    if iboss_login.status_code == 200:
        iboss_login_json = iboss_login.json()
        iboss_login_session = iboss_login_json['sessionId']
        iboss_netid_url = 'https://' + iboss_node + '/json/computers/dynamic/all?sessionId=' + iboss_login_session
        iboss_netid = requests.delete(iboss_netid_url, proxies=proxies, verify=False)
        if iboss_netid.status_code == 200:
            print("success on %s", iboss_node)
            iboss_netid_json = iboss_netid.json()
            iboss_netid_details = iboss_netid_json['message']
            ibnd[iboss_node] = len(iboss_netid_details)
        else:
            ibnd[iboss_node] = 0
    else:
        pass

def manager(iboss_nodes = []):
    manager = Manager()
    ibnd = manager.dict()
    pool = Pool(cpu_count())
    for iboss_node in iboss_nodes:
        pool.apply_async(delete_iboss_netid, args=(ibnd,iboss_node) )
    pool.close()
    pool.join()
    return dict(ibnd)

# def get_ibossnode_with_no_ssl(ssl_dict = {}):
#     ch2_primary_ssl = ssl_dict['ccspcg-ch2-01p.wco.comcast.net']
#     defective_nodes_list = []
#     for node in ssl_dict:
#         if node == 'ccspcg-ch2-01p.wco.comcast.net':
#             pass
#         else:
#             if ssl_dict[node] < ch2_primary_ssl:
#                 defective_nodes_list.append(node)
#             else:
#                 pass
#     if len(defective_nodes_list) != 0:
#         return defective_nodes_list
#     else:
#         return None
	
if __name__ == "__main__":
    iboss_netid_results = manager(iboss_details_list)
    print(iboss_netid_results)
    # defective_nodes = get_ibossnode_with_no_ssl(iboss_ssl_results)
    # force_sync_nodes_success=[]
    # force_sync_nodes_fail=[]
    # if defective_nodes == None:
    #     print("All iBoss Nodes are Healthy! (SSL Lists are in Sync)")
    #     sendMail('<EMAIL>', ['<EMAIL>'], 'Success iBoss SSL Report: '+day, email_temp_s, [])
    # else:
    #     with open(iboss_out_file, 'w') as f:
    #         for node in defective_nodes:
    #             f.write(f"{node}\n")
    #     for node in defective_nodes:
    #         sync_status = force_sync_iboss_node(node)
    #         if sync_status == True:
    #             force_sync_nodes_success.append(node)
    #         else:
    #             force_sync_nodes_fail.append(node)
    #     fin_defective_nodes = "\n".join(defective_nodes)
    #     fin_force_sync_nodes_success = "\n".join(force_sync_nodes_success)
    #     fin_force_sync_nodes_fail = "\n".join(force_sync_nodes_fail)
    #     fin_email_temp_f = email_temp_f1 + "\n" + fin_defective_nodes + "\n\n\n" + "Force Sync Successful Nodes:" + "\n" + fin_force_sync_nodes_success + "\n\n" + "Force Sync Failed Nodes:" + "\n" + fin_force_sync_nodes_fail + "\n" + email_temp_f2
    #     print(f"Some defective iboss nodes have been identified and listed below:\n{fin_defective_nodes}\nForce Sync has been triggered on these nodes")
    #     sendMail('<EMAIL>', ['<EMAIL>'], 'Failure iBoss SSL Report: '+day, fin_email_temp_f, [iboss_out_file])