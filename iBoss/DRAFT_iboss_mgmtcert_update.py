import requests
import base64
import os
import time
import pandas as pd
from multiprocessing import Process, Manager, Pool, cpu_count
import urllib3
import json

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


iboss_admin = base64.b64decode('YWRtaW4=').decode('utf-8')
iboss_pass = base64.b64decode('c2llYzBtY2FzdCE=').decode('utf-8')

basedir = os.path.dirname(os.path.abspath(__file__))
indir = basedir + '\\input'

# On-Prem iboss Details
# iboss_details = indir + '\\iboss_details.csv'
iboss_details = indir + '\\iboss_lab_details.csv'
iboss_details_df = pd.read_csv(iboss_details)
iboss_details_list = iboss_details_df['name'].to_list()
iboss_cert_file = indir + '\\iboss_cert.json'
with open (iboss_cert_file, 'r') as f:
    iboss_cert = json.load(f)


def update_iboss_cert(iboss_node, iboss_cert):
    proxies = {'https': 'ccspcg-ash-01p.wco.comcast.net:8083'}
    data = {"userName":iboss_admin, "x":iboss_pass, "ldapServer":""}
    login_url = 'https://' + iboss_node + '/json/login'
    iboss_login = requests.post(login_url, headers={'Origin': iboss_node}, json=data, proxies=proxies, verify=False)
    if iboss_login.status_code == 200:
        iboss_login_json = iboss_login.json()
        iboss_login_session = iboss_login_json['sessionId']
        iboss_login_authtoken = 'AUTH-TOKEN=0&' + iboss_login_session + '; XSRF-TOKEN='
        headers={'Origin': iboss_node, 'Cookie': iboss_login_authtoken, 'Content-Type': 'application/json'}
        # iboss_cert_url = 'https://' + iboss_node + '/json/network/sslSettings?sessionId=' + iboss_login_session
        iboss_cert_url = 'https://' + iboss_node + '/json/network/sslSettings'
        iboss_cert_change = requests.post(iboss_cert_url, headers=headers, data=iboss_cert, proxies=proxies, verify=False)
        if iboss_cert_change.status_code == 200:
            print(f"success on {iboss_node:30}")
        else:
            print(f"failure on {iboss_node:30}")
    else:
        pass

def manager(iboss_nodes = []):
    manager = Manager()
    ibnd = manager.dict()
    pool = Pool(cpu_count())
    for iboss_node in iboss_nodes:
        pool.apply_async(update_iboss_cert, args=(iboss_node, iboss_cert) )
    pool.close()
    pool.join()

	
if __name__ == "__main__":
    iboss_route_results_ash = manager(iboss_details_list, iboss_cert)
    print(iboss_route_results_ash)