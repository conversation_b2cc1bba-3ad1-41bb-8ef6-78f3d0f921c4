import requests
import urllib3
import os
import time
import pandas as pd
from multiprocessing import Manager, Pool, cpu_count

proxy_exception = (requests.exceptions.ProxyError, OSError, urllib3.exceptions.MaxRetryError)
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

basedir = os.path.dirname(os.path.abspath(__file__))
indir = basedir + '/input'
outdir = basedir + '/output'

day = time.strftime('%b_%d_%y')
iboss_details = indir + '/iboss_corp_ip_details.csv'
iboss_details_df = pd.read_csv(iboss_details)
iboss_details_list = iboss_details_df['hostname'].to_list()
iboss_details_df = iboss_details_df.set_index("hostname")
csv_file = outdir + '/iboss_corp_ip_updated.csv'

if os.path.exists(csv_file):
    os.remove(csv_file)


def get_iboss_ip_details(ns,ipd,iboss_node):
    proxies = {'https': 'http://'+iboss_node+':80'}
    df1 = ns.df1
    description = df1.loc[iboss_node]['description']
    hostname = iboss_node
    check_v4_url = 'https://api.ipify.org?format=json'
    check_v6_url = 'https://api6.ipify.org?format=json'
    try:
        v4_request = requests.get(check_v4_url, proxies=proxies, verify=False)
        if v4_request.status_code == 200:
            v4_request_json = v4_request.json()
            v4_ip = v4_request_json['ip']
            print(v4_ip)
            ipv4 = v4_ip
        else:
            ipv4 = 0
    except proxy_exception:
        ipv4 = 'proxy_error'
    try:
        v6_request = requests.get(check_v6_url, proxies=proxies, verify=False)
        if v6_request.status_code == 200:
            v6_request_json = v6_request.json()
            v6_ip = v6_request_json['ip']
            print(v6_ip)
            ipv6 = v6_ip
        else:
            ipv6 = 0
    except proxy_exception:
        ipv6 = 'proxy_error'
    temp_df = pd.DataFrame([[hostname,description,ipv4,ipv6]], columns=['hostname','description','ipv4','ipv6'])
    print(temp_df)
    ipd[iboss_node]=temp_df

def manager(df1, lst = []):
    manager = Manager()
    ipd = manager.dict()
    ns = manager.Namespace()
    ns.df1 = df1
    pool = Pool(cpu_count())
    for ite in lst:
        pool.apply_async(get_iboss_ip_details, args=(ns,ipd,ite) )
    pool.close()
    pool.join()
    return ipd

	
if __name__ == "__main__":
    results = manager(iboss_details_df, iboss_details_list)
    with open(csv_file, 'a') as f:
        f.write('hostname,description,ipv4,ipv6')
        f.write('\n')
    for k in results:
        results[k].to_csv(csv_file, mode='a', index=False, header=False, lineterminator='\n')
    exit()