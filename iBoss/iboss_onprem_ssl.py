import requests
import sys
import base64
import os
import time
import pandas as pd
from multiprocessing import Process, Manager, Pool, cpu_count
import urllib3
import smtplib,ssl
# import netmiko
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.base import MIMEBase
from email.mime.text import MIMEText
from email.utils import formatdate
from email import encoders
from email.utils import COMMASPACE, formatdate
# from netmiko.f5.f5_tmsh_ssh import F5TmshSSH as f5t
# from netmiko.f5.f5_linux_ssh import F5LinuxSSH as f5l

# netmiko_exceptions = (netmiko.exceptions.NetMikoTimeoutException,
#                       netmiko.exceptions.NetMikoAuthenticationException,
#                       IOError)

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

sec_path = b'L2FwcC9zaWVfcHJvamVjdHMvc2llX2lib3NzL2lucHV0'
secr_path = (base64.b64decode(sec_path)).decode('utf-8')
sys.path.insert(1, secr_path)
from iboss_get_secrets import *
iboss_admin = get_sec(iboss_admin)
iboss_pass = get_sec(iboss_pass)
f5_api_auth = get_sec(f5_api_auth)
# u = get_sec(f5_usr)
# p = get_sec(f5_pwd)


basedir = os.path.dirname(os.path.abspath(__file__))
indir = basedir + '/input'
outdir = basedir + '/output'

day = time.strftime('%b_%d_%y')
iboss_details = indir + '/iboss_details.csv'
iboss_details_df = pd.read_csv(iboss_details)
iboss_details_list = iboss_details_df['name'].to_list()
iboss_details_df = iboss_details_df.set_index("name")
iboss_out_file = outdir + '/iboss_ssl_list_' + day + '.txt'
f_email_temp_s = indir + '/success_template.txt'
f_email_temp_f1 = indir + '/failure_template.txt'
f_email_temp_f2 = indir + '/failure_sign_template.txt'

if os.path.exists(iboss_out_file):
    os.remove(iboss_out_file)

with open(f_email_temp_s, 'r') as f:
    email_temp_s = f.read()
with open(f_email_temp_f1, 'r') as f:
    email_temp_f1 = f.read()
with open(f_email_temp_f2, 'r') as f:
    email_temp_f2 = f.read()

# def connect_device(ip):
#     x = f5l(ip=ip, username=u, password=p)
#     # x = f5t(ip=ip, username=u, password=p)
#     return x

def sendMail(sfrom, to, subject, text, files=[]):
    msg = MIMEMultipart()
    msg['To'] = COMMASPACE.join(to)
    msg['Date'] = formatdate(localtime=True)
    msg['Subject'] = subject
    
    msg.attach( MIMEText(text) )
    
    for file in files:
        part = MIMEBase('application', "octet-stream")
        part.set_payload( open(file,"rb").read() )
        encoders.encode_base64(part)
        part.add_header('Content-Disposition', 'attachment; filename="%s"'
                       % os.path.basename(file))
        msg.attach(part)
    
    server = smtplib.SMTP('mailrelay.comcast.com')
    server.sendmail(sfrom, to, msg.as_string())
    print ('Done', flush=True)
    server.quit()

def get_iboss_ssl_details(ibnd,iboss_node):
    proxies = {'https': 'ccspcg-ash-01p.wco.comcast.net:8083'}
    data = {"userName":iboss_admin, "x":iboss_pass, "ldapServer":""}
    login_url = 'https://' + iboss_node + '/json/login'
    iboss_login = requests.post(login_url, json=data, proxies=proxies, verify=False)
    if iboss_login.status_code == 200:
        iboss_login_json = iboss_login.json()
        iboss_login_session = iboss_login_json['sessionId']
        iboss_ssl_url = 'https://' + iboss_node + '/json/network/sslDecryption/domains?sessionId=' + iboss_login_session
        iboss_ssl = requests.get(iboss_ssl_url, proxies=proxies, verify=False)
        if iboss_ssl.status_code == 200:
            iboss_ssl_json = iboss_ssl.json()
            iboss_ssl_details = iboss_ssl_json['entries']
            ibnd[iboss_node] = len(iboss_ssl_details)
        else:
            ibnd[iboss_node] = 0
    else:
        pass

def force_sync_iboss_node(iboss_node):
    proxies = {'https': 'ccspcg-ash-01p.wco.comcast.net:8083'}
    data = {"userName":iboss_admin, "x":iboss_pass, "ldapServer":""}
    login_url = 'https://' + iboss_node + '/json/login'
    iboss_login = requests.post(login_url, json=data, proxies=proxies, verify=False)
    if iboss_login.status_code == 200:
        iboss_login_json = iboss_login.json()
        iboss_login_session = iboss_login_json['sessionId']
        iboss_forcesync_url = 'https://' + iboss_node + '/json/network/clustering/forceSync?sessionId=' + iboss_login_session
        iboss_forcesync = requests.get(iboss_forcesync_url, proxies=proxies, verify=False)
        if iboss_forcesync.status_code == 200:
            return True
        else:
            return False
    else:
        return False

def manager(iboss_nodes = []):
    manager = Manager()
    ibnd = manager.dict()
    pool = Pool(cpu_count())
    for iboss_node in iboss_nodes:
        pool.apply_async(get_iboss_ssl_details, args=(ibnd,iboss_node) )
    pool.close()
    pool.join()
    return dict(ibnd)

def get_ibossnode_with_no_ssl(ssl_dict = {}):
    ch2_primary_ssl = ssl_dict['ccspcg-ch2-01p.wco.comcast.net']
    defective_nodes_list = []
    for node in ssl_dict:
        if node == 'ccspcg-ch2-01p.wco.comcast.net':
            pass
        else:
            if ssl_dict[node] < ch2_primary_ssl:
                defective_nodes_list.append(node)
            else:
                pass
    if len(defective_nodes_list) != 0:
        return defective_nodes_list
    else:
        return None

def disable_f5_pool_members_with_no_ssl(iboss_df, iboss_list):
    # print("Entering F5 section")
    for i in iboss_df.index:
        f5 = iboss_df.loc[i]['lb_wco']
        if iboss_df.loc[i]['generic_pool_name_v4'] == 'bypass':
            pass
        else:
            f5_gen_pool_v4 = iboss_df.loc[i]['generic_pool_name_v4']
            f5_gen_member_v4 = iboss_df.loc[i]['generic_member_name_v4']
            f5_gen_pool_v6 = iboss_df.loc[i]['generic_pool_name_v6']
            f5_gen_member_v6 = iboss_df.loc[i]['generic_member_name_v6']
            f5_header = {'Content-Type':'application/json', 'Authorization':f5_api_auth}
            f5_data = {'session':'user-enabled', 'state':'user-up'}
            f5_url_v4 = 'https://' + f5 + '/mgmt/tm/ltm/pool/' + f5_gen_pool_v4 + '/members/~Common~' + f5_gen_member_v4 + ':0'
            # print(f5_url_v4)
            f5_url_v4_request = requests.patch(f5_url_v4, json=f5_data, headers=f5_header, verify=False)
            # print(f5_url_v4_request.status_code)
            f5_url_v6 = 'https://' + f5 + '/mgmt/tm/ltm/pool/' + f5_gen_pool_v6 + '/members/~Common~' + f5_gen_member_v6 + ':0'
            # print(f5_url_v6)
            f5_url_v6_request = requests.patch(f5_url_v6, json=f5_data, headers=f5_header, verify=False)
            # print(f5_url_v6_request.status_code)
            # gen_command_v4 = 'tmsh modify ltm pool ' + f5_gen_pool_v4 + ' members modify {' + f5_gen_member_v4 + ':any {state user-up session user-enabled} }'
            # gen_command_v6 = 'tmsh modify ltm pool ' + f5_gen_pool_v6 + ' members modify {' + f5_gen_member_v6 + ':any {state user-up session user-enabled} }'
            # print(gen_command_v4)
            # print(gen_command_v6)
            # connection = connect_device(f5)
            # print("Connection successfull")
            # pre_gen_command_v4_result = connection.send_command(gen_command_v4)
            # pre_gen_command_v6_result = connection.send_command(gen_command_v6)
            # connection.disconnect()
            # print("Disconnect successfull")
        if iboss_df.loc[i]['avd_pool_name'] == 'bypass':
            pass
        else:
            f5_avd_pool_v4 = iboss_df.loc[i]['avd_pool_name']
            f5_avd_member_v4 = iboss_df.loc[i]['avd_member_name']
            f5_header = {'Content-Type':'application/json', 'Authorization':f5_api_auth}
            f5_data = {'session':'user-enabled', 'state':'user-up'}
            f5_avd_url_v4 = 'https://' + f5 + '/mgmt/tm/ltm/pool/' + f5_avd_pool_v4 + '/members/~Common~' + f5_avd_member_v4 + ':8083'
            # print(f5_avd_url_v4)
            f5_avd_url_v4_request = requests.patch(f5_avd_url_v4, json=f5_data, headers=f5_header, verify=False)
            # print(f5_avd_url_v4_request.status_code)
            # avd_command_v4 = 'tmsh modify ltm pool ' + f5_avd_pool_v4 + ' members modify {' + f5_avd_member_v4 + ':us-srv {state user-up session user-enabled} }'
            # print(avd_command_v4)
            # connection = connect_device(f5)
            # print("Connection successfull")
            # pre_avd_command_v4_result = connection.send_command(avd_command_v4)
            # connection.disconnect()
            # print("Disconnect successfull")
    if iboss_list == None:
        pass
    else:
        for iboss_defective_node in iboss_list:
            f5 = iboss_df.loc[iboss_defective_node]['lb_wco']
            if iboss_df.loc[iboss_defective_node]['generic_pool_name_v4'] == 'bypass':
                pass
            else:
                f5_gen_pool_v4 = iboss_df.loc[iboss_defective_node]['generic_pool_name_v4']
                f5_gen_member_v4 = iboss_df.loc[iboss_defective_node]['generic_member_name_v4']
                f5_gen_pool_v6 = iboss_df.loc[iboss_defective_node]['generic_pool_name_v6']
                f5_gen_member_v6 = iboss_df.loc[iboss_defective_node]['generic_member_name_v6']
                f5_header = {'Content-Type':'application/json', 'Authorization':f5_api_auth}
                f5_data = {'session':'user-disabled', 'state':'user-down'}
                f5_url_v4 = 'https://' + f5 + '/mgmt/tm/ltm/pool/' + f5_gen_pool_v4 + '/members/~Common~' + f5_gen_member_v4 + ':0'
                # print(f5_url_v4)
                f5_url_v4_request = requests.patch(f5_url_v4, json=f5_data, headers=f5_header, verify=False)
                # print(f5_url_v4_request.status_code)
                f5_url_v6 = 'https://' + f5 + '/mgmt/tm/ltm/pool/' + f5_gen_pool_v6 + '/members/~Common~' + f5_gen_member_v6 + ':0'
                # print(f5_url_v6)
                f5_url_v6_request = requests.patch(f5_url_v6, json=f5_data, headers=f5_header, verify=False)
                # print(f5_url_v6_request.status_code)
                # gen_command_v4 = 'tmsh modify ltm pool ' + f5_gen_pool_v4 + ' members modify {' + f5_gen_member_v4 + ':any {state user-down session user-disabled} }'
                # gen_command_v6 = 'tmsh modify ltm pool ' + f5_gen_pool_v6 + ' members modify {' + f5_gen_member_v6 + ':any {state user-down session user-disabled} }'
                # connection = connect_device(f5)
                # gen_command_v4_result = connection.send_command(gen_command_v4)
                # gen_command_v6_result = connection.send_command(gen_command_v6)
                # connection.disconnect()
            if iboss_df.loc[iboss_defective_node]['avd_pool_name'] == 'bypass':
                pass
            else:
                f5_avd_pool_v4 = iboss_df.loc[iboss_defective_node]['avd_pool_name']
                f5_avd_member_v4 = iboss_df.loc[iboss_defective_node]['avd_member_name']
                f5_header = {'Content-Type':'application/json', 'Authorization':f5_api_auth}
                f5_data = {'session':'user-disabled', 'state':'user-down'}
                f5_avd_url_v4 = 'https://' + f5 + '/mgmt/tm/ltm/pool/' + f5_avd_pool_v4 + '/members/~Common~' + f5_avd_member_v4 + ':8083'
                # print(f5_avd_url_v4)
                f5_avd_url_v4_request = requests.patch(f5_avd_url_v4, json=f5_data, headers=f5_header, verify=False)
                # print(f5_avd_url_v4_request.status_code)
                # avd_command_v4 = 'tmsh modify ltm pool ' + f5_avd_pool_v4 + ' members modify {' + f5_avd_member_v4 + ':us-srv {state user-down session user-disabled} }'
                # connection = connect_device(f5)
                # avd_command_v4_result = connection.send_command(avd_command_v4)
                # connection.disconnect()

	
if __name__ == "__main__":
    iboss_ssl_results = manager(iboss_details_list)
    defective_nodes = get_ibossnode_with_no_ssl(iboss_ssl_results)
    disable_f5_pool_members_with_no_ssl(iboss_details_df,defective_nodes)
    force_sync_nodes_success=[]
    force_sync_nodes_fail=[]
    if defective_nodes == None:
        print("All iBoss Nodes are Healthy! (SSL Lists are in Sync)")
        # sendMail('<EMAIL>', ['<EMAIL>'], 'Success iBoss SSL Report: '+day, email_temp_s, [])
        sendMail('<EMAIL>', ['<EMAIL>'], 'Success iBoss SSL Report: '+day, email_temp_s, [])
    else:
        with open(iboss_out_file, 'w') as f:
            for node in defective_nodes:
                f.write(f"{node}\n")
        for node in defective_nodes:
            sync_status = force_sync_iboss_node(node)
            if sync_status == True:
                force_sync_nodes_success.append(node)
            else:
                force_sync_nodes_fail.append(node)
        fin_defective_nodes = "\n".join(defective_nodes)
        fin_force_sync_nodes_success = "\n".join(force_sync_nodes_success)
        fin_force_sync_nodes_fail = "\n".join(force_sync_nodes_fail)
        fin_email_temp_f = email_temp_f1 + "\n" + fin_defective_nodes + "\n\n\n" + "Force Sync Successful Nodes:" + "\n" + fin_force_sync_nodes_success + "\n\n" + "Force Sync Failed Nodes:" + "\n" + fin_force_sync_nodes_fail + "\n" + email_temp_f2
        print(f"Some defective iboss nodes have been identified and listed below:\n{fin_defective_nodes}\nForce Sync has been triggered on these nodes")
        # sendMail('<EMAIL>', ['<EMAIL>'], 'Failure iBoss SSL Report: '+day, fin_email_temp_f, [iboss_out_file])
        sendMail('<EMAIL>', ['<EMAIL>'], 'Failure iBoss SSL Report: '+day, fin_email_temp_f, [iboss_out_file])