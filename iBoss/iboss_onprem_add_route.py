import requests
import base64
import os
import time
import pandas as pd
from multiprocessing import Process, Manager, Pool, cpu_count
import urllib3
import json

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


iboss_admin = base64.b64decode('YWRtaW4=').decode('utf-8')
iboss_pass = base64.b64decode('c2llYzBtY2FzdCE=').decode('utf-8')

basedir = os.path.dirname(os.path.abspath(__file__))
indir = basedir + '\\input'

# Ashburn Details
iboss_details_ash = indir + '\\iboss_details_ash.csv'
iboss_details_ash_df = pd.read_csv(iboss_details_ash)
iboss_details_ash_list = iboss_details_ash_df['name'].to_list()
iboss_route_file_ash = indir + '\\iboss_route_details_ash.csv'
ibr_ash_df = pd.read_csv(iboss_route_file_ash)
iboss_route_ash_dict = ibr_ash_df.to_dict(orient='records')

# Chicago Details
iboss_details_ch2 = indir + '\\iboss_details_ch2.csv'
iboss_details_ch2_df = pd.read_csv(iboss_details_ch2)
iboss_details_ch2_list = iboss_details_ch2_df['name'].to_list()
iboss_route_file_ch2 = indir + '\\iboss_route_details_ch2.csv'
ibr_ch2_df = pd.read_csv(iboss_route_file_ch2)
iboss_route_ch2_dict = ibr_ch2_df.to_dict(orient='records')

# Hillsboro Details
iboss_details_ho = indir + '\\iboss_details_ho.csv'
# iboss_details_ho = indir + '\\iboss_details_ho_test.csv'
iboss_details_ho_df = pd.read_csv(iboss_details_ho)
iboss_details_ho_list = iboss_details_ho_df['name'].to_list()
iboss_route_file_ho = indir + '\\iboss_route_details_ho.csv'
ibr_ho_df = pd.read_csv(iboss_route_file_ho)
iboss_route_ho_dict = ibr_ho_df.to_dict(orient='records')

# Westchester Details
iboss_details_wc = indir + '\\iboss_details_wc.csv'
iboss_details_wc_df = pd.read_csv(iboss_details_wc)
iboss_details_wc_list = iboss_details_wc_df['name'].to_list()
iboss_route_file_wc = indir + '\\iboss_route_details_wc.csv'
ibr_wc_df = pd.read_csv(iboss_route_file_wc)
iboss_route_wc_dict = ibr_wc_df.to_dict(orient='records')

def add_iboss_route(ibnd, iboss_node, iboss_route_dict):
    proxies = {'https': 'ccspcg-ash-01p.wco.comcast.net:8083'}
    data = {"userName":iboss_admin, "x":iboss_pass, "ldapServer":""}
    login_url = 'https://' + iboss_node + '/json/login'
    iboss_login = requests.post(login_url, headers={'Origin': iboss_node}, json=data, proxies=proxies, verify=False)
    if iboss_login.status_code == 200:
        iboss_login_json = iboss_login.json()
        iboss_login_session = iboss_login_json['sessionId']
        iboss_login_authtoken = 'AUTH-TOKEN=0&' + iboss_login_session + '; XSRF-TOKEN='
        headers={'Origin': iboss_node, 'Cookie': iboss_login_authtoken, 'Content-Type': 'application/json'}
        for route in iboss_route_dict:
            payload = json.dumps(route)
            iboss_route_url = 'https://' + iboss_node + '/json/network/routes?sessionId=' + iboss_login_session
            iboss_route = requests.put(iboss_route_url, headers=headers, data=payload, proxies=proxies, verify=False)
            if iboss_route.status_code == 200:
                print(f"success on {iboss_node:30}")
                iboss_route_json = iboss_route.json()
                iboss_route_details = iboss_route_json['message']
                ibnd[iboss_node] = iboss_route_details
            else:
                ibnd[iboss_node] = 0
                print(f"failure on {iboss_node:30}")
    else:
        pass

def manager(iboss_nodes = [], iboss_route_dict = {}):
    manager = Manager()
    ibnd = manager.dict()
    pool = Pool(cpu_count())
    for iboss_node in iboss_nodes:
        pool.apply_async(add_iboss_route, args=(ibnd,iboss_node,iboss_route_dict) )
    pool.close()
    pool.join()
    return dict(ibnd)

	
if __name__ == "__main__":
    iboss_route_results_ash = manager(iboss_details_ash_list, iboss_route_ash_dict)
    iboss_route_results_ch2 = manager(iboss_details_ch2_list, iboss_route_ch2_dict)
    iboss_route_results_ho = manager(iboss_details_ho_list, iboss_route_ho_dict)
    iboss_route_results_wc = manager(iboss_details_wc_list, iboss_route_wc_dict)
    print(iboss_route_results_ash)
    print(iboss_route_results_ch2)
    print(iboss_route_results_ho)
    print(iboss_route_results_wc)